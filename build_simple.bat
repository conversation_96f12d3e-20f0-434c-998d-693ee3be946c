@echo off
chcp 65001 >nul
echo 🏨 房间管理系统简化打包脚本
echo ================================

echo.
echo 📋 步骤1: 检查Python环境...
python --version
if errorlevel 1 (
    echo ❌ 错误: 未找到Python环境
    echo 请确保Python已正确安装并添加到PATH
    pause
    exit /b 1
)

echo.
echo 📋 步骤2: 安装PyInstaller...
pip install pyinstaller
if errorlevel 1 (
    echo ❌ 错误: PyInstaller安装失败
    pause
    exit /b 1
)

echo.
echo 📋 步骤3: 开始打包应用程序...
pyinstaller --onefile --windowed --name "房间管理系统" --clean "房间管理系统2.py"
if errorlevel 1 (
    echo ❌ 错误: 打包失败
    pause
    exit /b 1
)

echo.
echo 📋 步骤4: 创建发布目录...
if not exist "release" mkdir "release"
copy "dist\房间管理系统.exe" "release\"
if exist "rooms.json" copy "rooms.json" "release\"
if exist "persons.json" copy "persons.json" "release\"
if exist "financial_data.json" copy "financial_data.json" "release\"

echo.
echo 📋 步骤5: 清理临时文件...
if exist "build" rmdir /s /q "build"
if exist "__pycache__" rmdir /s /q "__pycache__"
if exist "房间管理系统.spec" del "房间管理系统.spec"

echo.
echo ✅ 打包完成!
echo 📁 可执行文件位于: release\房间管理系统.exe
echo 💡 可以将release目录中的所有文件分发给用户
echo.
pause
