@echo off
chcp 65001 >nul
echo 🏨 房间管理系统快速打包
echo ========================

echo 正在执行打包命令...
pyinstaller --onefile --windowed --name 房间管理系统 --clean --icon app_icon.ico --add-data rooms.json;. --add-data persons.json;. --add-data financial_data.json;. --hidden-import tkinter --hidden-import tkinter.ttk --hidden-import tkinter.messagebox --hidden-import tkinter.simpledialog 房间管理系统2.py

if errorlevel 1 (
    echo ❌ 打包失败
    pause
    exit /b 1
)

echo ✅ 打包成功!
echo 📁 可执行文件位于: dist\房间管理系统.exe

if not exist "release" mkdir "release"
copy "dist\房间管理系统.exe" "release\"

echo 📦 发布文件已复制到 release 目录
pause
