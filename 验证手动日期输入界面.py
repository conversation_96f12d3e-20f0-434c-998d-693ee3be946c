#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证房间管理系统中手动日期输入界面的访问路径和显示状态
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, timedelta
import sys
import os

def create_test_interface():
    """创建测试界面来验证手动日期输入功能"""
    
    root = tk.Tk()
    root.title("手动日期输入界面验证")
    root.geometry("700x600")
    root.configure(bg="#f8f9fa")
    
    # 主框架
    main_frame = tk.Frame(root, bg="#f8f9fa", padx=20, pady=20)
    main_frame.pack(fill="both", expand=True)
    
    # 标题
    title_label = tk.Label(main_frame, 
                          text="房间管理系统 - 手动日期输入功能验证", 
                          font=("Microsoft YaHei UI", 16, "bold"),
                          bg="#f8f9fa",
                          fg="#1e3a8a")
    title_label.pack(pady=(0, 20))
    
    # 说明文本
    instruction_text = """
    本测试界面用于验证房间管理系统中手动输入起租日期的功能。
    
    在实际系统中，手动日期输入功能应该在以下操作流程中出现：
    
    1. 点击房间卡片进入房间管理窗口
    2. 在房间管理窗口中选择租户
    3. 点击"确认选择"后进入租期选择窗口
    4. 在租期选择窗口中应该看到日期输入模式选择选项
    
    下面是手动日期输入界面的模拟：
    """
    
    instruction_label = tk.Label(main_frame, 
                               text=instruction_text,
                               font=("Microsoft YaHei UI", 10),
                               bg="#f8f9fa",
                               fg="#64748b",
                               justify="left")
    instruction_label.pack(pady=(0, 20), anchor="w")
    
    # 创建模拟的租期选择界面
    demo_frame = tk.LabelFrame(main_frame, 
                              text="模拟：租期选择窗口", 
                              font=("Microsoft YaHei UI", 12, "bold"),
                              bg="#ffffff",
                              fg="#1e3a8a",
                              padx=20, 
                              pady=20)
    demo_frame.pack(fill="both", expand=True, pady=(0, 20))
    
    # 租期选择
    period_frame = tk.Frame(demo_frame, bg="#ffffff")
    period_frame.pack(fill="x", pady=(0, 15))
    
    tk.Label(period_frame, 
            text="租期类型:", 
            font=("Microsoft YaHei UI", 12, "bold"),
            bg="#ffffff").pack(side="left")
    
    period_var = tk.StringVar(value="月租")
    period_combobox = ttk.Combobox(period_frame, 
                                  textvariable=period_var, 
                                  values=["周租", "月租", "季租", "半年租", "年租"], 
                                  state="readonly",
                                  width=12)
    period_combobox.pack(side="right")
    
    # 日期模式选择 - 这是关键功能
    mode_frame = tk.Frame(demo_frame, bg="#ffffff")
    mode_frame.pack(fill="x", pady=(0, 15))
    
    tk.Label(mode_frame, 
            text="📅 起租日期设置:", 
            font=("Microsoft YaHei UI", 12, "bold"),
            bg="#ffffff",
            fg="#1e3a8a").pack(side="left")
    
    date_mode_var = tk.StringVar(value="auto")
    
    # 自动计算选项
    auto_radio = tk.Radiobutton(mode_frame,
                              text="自动计算（今天）",
                              variable=date_mode_var,
                              value="auto",
                              font=("Microsoft YaHei UI", 10),
                              bg="#ffffff",
                              activebackground="#ffffff")
    auto_radio.pack(side="right", padx=(10, 0))
    
    # 手动输入选项
    manual_radio = tk.Radiobutton(mode_frame,
                                text="手动输入",
                                variable=date_mode_var,
                                value="manual",
                                font=("Microsoft YaHei UI", 10),
                                bg="#ffffff",
                                activebackground="#ffffff")
    manual_radio.pack(side="right", padx=(10, 0))
    
    # 起租日期输入
    date_input_frame = tk.Frame(demo_frame, bg="#ffffff")
    date_input_frame.pack(fill="x", pady=(0, 10))
    
    tk.Label(date_input_frame, 
            text="起租日期:", 
            font=("Microsoft YaHei UI", 12),
            bg="#ffffff").pack(side="left")
    
    start_date_entry = tk.Entry(date_input_frame, 
                               font=("Microsoft YaHei UI", 11),
                               width=15,
                               justify="center",
                               relief="solid",
                               bd=1)
    start_date_entry.pack(side="right")
    
    # 日期格式提示
    hint_frame = tk.Frame(demo_frame, bg="#ffffff")
    hint_frame.pack(fill="x", pady=(0, 15))
    
    hint_label = tk.Label(hint_frame, 
                         text="(格式: YYYY-MM-DD，如: 2024-01-15)", 
                         font=("Microsoft YaHei UI", 9),
                         bg="#ffffff",
                         fg="#64748b")
    hint_label.pack(side="right")
    
    # 截止日期显示
    end_date_frame = tk.Frame(demo_frame, bg="#ffffff")
    end_date_frame.pack(fill="x", pady=(0, 20))
    
    tk.Label(end_date_frame, 
            text="截止日期:", 
            font=("Microsoft YaHei UI", 12),
            bg="#ffffff").pack(side="left")
    
    end_date_entry = tk.Entry(end_date_frame, 
                             font=("Microsoft YaHei UI", 11),
                             width=15,
                             justify="center",
                             state="readonly",
                             relief="solid",
                             bd=1)
    end_date_entry.pack(side="right")
    
    # 状态显示
    status_frame = tk.Frame(demo_frame, bg="#ffffff")
    status_frame.pack(fill="x", pady=(0, 20))
    
    status_label = tk.Label(status_frame, 
                           text="当前模式: 自动计算", 
                           font=("Microsoft YaHei UI", 10, "bold"),
                           bg="#ffffff",
                           fg="#059669")
    status_label.pack()
    
    # 更新日期函数
    def update_dates(*args):
        mode = date_mode_var.get()
        period = period_var.get()
        
        if mode == "auto":
            # 自动计算模式
            today = datetime.now().date()
            start_date = today
            
            start_date_entry.config(state="normal", bg="#f1f5f9")
            start_date_entry.delete(0, tk.END)
            start_date_entry.insert(0, start_date.strftime("%Y-%m-%d"))
            start_date_entry.config(state="readonly")
            
            status_label.config(text="当前模式: 自动计算（使用今天日期）", fg="#059669")
            
        else:
            # 手动输入模式
            start_date_entry.config(state="normal", bg="#ffffff")
            current_value = start_date_entry.get()
            if not current_value or current_value == datetime.now().date().strftime("%Y-%m-%d"):
                start_date_entry.delete(0, tk.END)
                start_date_entry.insert(0, datetime.now().date().strftime("%Y-%m-%d"))
            
            status_label.config(text="当前模式: 手动输入（可编辑日期）", fg="#7c3aed")
            
            try:
                start_date = datetime.strptime(start_date_entry.get(), "%Y-%m-%d").date()
            except ValueError:
                start_date = datetime.now().date()
        
        # 计算截止日期
        if period == "周租":
            end_date = start_date + timedelta(weeks=1)
        elif period == "月租":
            end_date = start_date + timedelta(days=30)
        elif period == "季租":
            end_date = start_date + timedelta(days=90)
        elif period == "半年租":
            end_date = start_date + timedelta(days=180)
        elif period == "年租":
            end_date = start_date + timedelta(days=365)
        else:
            end_date = start_date + timedelta(days=30)
        
        # 更新截止日期显示
        end_date_entry.config(state="normal")
        end_date_entry.delete(0, tk.END)
        end_date_entry.insert(0, end_date.strftime("%Y-%m-%d"))
        end_date_entry.config(state="readonly")
    
    # 绑定事件
    period_var.trace_add("write", update_dates)
    date_mode_var.trace_add("write", update_dates)
    
    def on_start_date_change(event):
        if date_mode_var.get() == "manual":
            update_dates()
    
    start_date_entry.bind('<KeyRelease>', on_start_date_change)
    start_date_entry.bind('<FocusOut>', on_start_date_change)
    
    # 测试按钮
    button_frame = tk.Frame(demo_frame, bg="#ffffff")
    button_frame.pack(fill="x", pady=(10, 0))
    
    def test_validation():
        start_date_str = start_date_entry.get().strip()
        if not start_date_str:
            messagebox.showerror("错误", "请输入起租日期")
            return
        
        try:
            start_date = datetime.strptime(start_date_str, "%Y-%m-%d").date()
            messagebox.showinfo("验证成功", 
                              f"起租日期格式正确: {start_date}\n"
                              f"租期类型: {period_var.get()}\n"
                              f"截止日期: {end_date_entry.get()}")
        except ValueError:
            messagebox.showerror("验证失败", "起租日期格式不正确，请使用 YYYY-MM-DD 格式")
    
    test_btn = tk.Button(button_frame, 
                        text="🔍 测试日期验证", 
                        command=test_validation,
                        font=("Microsoft YaHei UI", 10, "bold"),
                        bg="#3b82f6",
                        fg="white",
                        relief="flat",
                        padx=20,
                        pady=8)
    test_btn.pack(side="left")
    
    def show_access_path():
        access_info = """
        在实际房间管理系统中访问手动日期输入功能的步骤：
        
        1. 启动房间管理系统
        2. 在主界面找到要出租的房间卡片
        3. 点击房间卡片，打开房间管理窗口
        4. 在房间管理窗口中：
           - 如果房间状态为"闲置"，会看到租户列表
           - 选择一个或多个租户
           - 点击"确认选择"按钮
        5. 系统会打开"选择租期"窗口
        6. 在租期选择窗口中，您应该看到：
           - 租期类型下拉菜单
           - "起租日期设置"选项（自动计算 vs 手动输入）
           - 起租日期输入框
           - 截止日期显示框
        
        如果您没有看到这些选项，可能的原因：
        - 使用了旧版本的租房流程
        - 界面创建代码有问题
        - 事件绑定没有正确工作
        """
        
        messagebox.showinfo("访问路径说明", access_info)
    
    help_btn = tk.Button(button_frame, 
                        text="❓ 访问路径说明", 
                        command=show_access_path,
                        font=("Microsoft YaHei UI", 10),
                        bg="#10b981",
                        fg="white",
                        relief="flat",
                        padx=20,
                        pady=8)
    help_btn.pack(side="right")
    
    # 初始化
    update_dates()
    
    # 底部说明
    bottom_info = tk.Label(main_frame, 
                          text="如果在实际系统中没有看到上述界面，请检查代码中的 show_rent_period_selection_window 函数",
                          font=("Microsoft YaHei UI", 9),
                          bg="#f8f9fa",
                          fg="#dc2626")
    bottom_info.pack(pady=(10, 0))
    
    root.mainloop()

if __name__ == "__main__":
    print("启动手动日期输入界面验证工具...")
    create_test_interface()
