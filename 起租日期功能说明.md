# 房间管理系统 - 起租日期功能说明

## 功能概述

房间管理系统现已支持灵活的起租日期设置，用户可以选择自动计算日期或手动输入自定义的起租日期。

## 主要特性

### 1. 双模式支持
- **自动计算模式**：使用当前日期作为起租日期（保持原有功能）
- **手动输入模式**：允许用户输入自定义的起租日期

### 2. 日期验证
- 严格的日期格式验证（YYYY-MM-DD）
- 自动检测无效日期
- 友好的错误提示

### 3. 智能计算
- 根据选择的租期自动计算截止日期
- 支持周租、月租、季租、半年租、年租
- 实时更新截止日期显示

## 使用方法

### 在租房流程中使用

1. **选择租户**：首先选择要入住的租户
2. **设置租期**：在租期选择窗口中进行以下操作：

#### 自动计算模式（默认）
- 选择"自动计算（今天）"选项
- 系统将使用当前日期作为起租日期
- 起租日期输入框将显示为只读状态

#### 手动输入模式
- 选择"手动输入"选项
- 在起租日期输入框中输入自定义日期
- 日期格式必须为：YYYY-MM-DD（如：2024-01-15）
- 系统会实时验证日期格式并计算截止日期

3. **选择租期类型**：从下拉菜单中选择租期
4. **确认信息**：检查起租日期和截止日期是否正确
5. **完成租房**：点击确认按钮完成租房流程

## 日期格式要求

### 正确格式示例
- `2024-01-15`（2024年1月15日）
- `2024-12-31`（2024年12月31日）
- `2025-02-28`（2025年2月28日）

### 错误格式示例
- `2024-1-15`（月份缺少前导零）
- `2024-01-5`（日期缺少前导零）
- `24-01-15`（年份格式错误）
- `2024/01/15`（分隔符错误）
- `2024-13-15`（月份超出范围）
- `2024-02-30`（日期不存在）

## 功能验证

### 日期合理性检查
- 如果输入的起租日期是一年前的日期，系统会弹出确认对话框
- 用户可以选择继续使用该日期或重新输入

### 实时更新
- 切换日期模式时，界面会立即更新
- 修改起租日期或租期类型时，截止日期会自动重新计算
- 所有更改都会实时反映在界面上

## 租期计算规则

| 租期类型 | 计算方式 | 天数 |
|---------|---------|------|
| 周租 | 起租日期 + 7天 | 7天 |
| 月租 | 起租日期 + 30天 | 30天 |
| 季租 | 起租日期 + 90天 | 90天 |
| 半年租 | 起租日期 + 180天 | 180天 |
| 年租 | 起租日期 + 365天 | 365天 |

## 兼容性说明

### 向后兼容
- 现有的自动日期计算功能完全保留
- 默认模式仍为自动计算，保持用户习惯
- 所有现有数据和流程不受影响

### 数据存储
- 起租日期以YYYY-MM-DD格式存储在数据文件中
- 与现有数据格式完全兼容
- 支持数据导入导出

## 错误处理

### 常见错误及解决方案

1. **"请输入起租日期"**
   - 原因：在手动输入模式下未输入日期
   - 解决：在起租日期输入框中输入有效日期

2. **"起租日期格式不正确"**
   - 原因：输入的日期格式不符合YYYY-MM-DD要求
   - 解决：按照正确格式重新输入日期

3. **"起租日期是一年前的日期"**
   - 原因：输入的日期过于久远
   - 解决：确认日期是否正确，或选择更合适的日期

## 技术实现

### 核心功能
- 使用Python的datetime模块进行日期处理
- 实现严格的日期格式验证
- 支持实时日期计算和界面更新

### 用户界面
- 使用Tkinter创建现代化的用户界面
- 支持单选按钮切换输入模式
- 提供实时的日期格式提示

### 数据验证
- 多层次的日期验证机制
- 友好的错误提示和用户引导
- 智能的日期合理性检查

## 更新日志

### 版本更新内容
- ✅ 添加手动日期输入功能
- ✅ 保留现有自动计算逻辑
- ✅ 实现双模式切换界面
- ✅ 添加严格的日期格式验证
- ✅ 支持实时日期计算更新
- ✅ 确保与现有流程完全兼容

### 测试验证
- ✅ 自动计算模式测试通过
- ✅ 手动输入模式测试通过
- ✅ 日期格式验证测试通过
- ✅ 租期计算测试通过
- ✅ 界面交互测试通过
- ✅ 数据兼容性测试通过

## 使用建议

1. **日常使用**：建议使用自动计算模式，快速高效
2. **特殊情况**：需要指定特定起租日期时使用手动输入模式
3. **日期输入**：建议使用标准键盘输入，避免复制粘贴可能带来的格式问题
4. **数据备份**：在使用新功能前建议备份现有数据

## 技术支持

如果在使用过程中遇到问题，请检查：
1. 日期格式是否正确（YYYY-MM-DD）
2. 输入的日期是否真实存在
3. 是否选择了正确的输入模式
4. 租期类型是否已选择

---

*本功能已完全集成到房间管理系统中，无需额外安装或配置。*
