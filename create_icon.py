#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
为房间管理系统创建应用图标
"""

def create_simple_icon():
    """创建简单的文本图标（不需要PIL）"""
    # 创建一个简单的ICO文件头
    # 这是一个最小的16x16像素的ICO文件
    ico_data = bytes([
        # ICO文件头
        0x00, 0x00,  # 保留字段
        0x01, 0x00,  # 图像类型 (1 = ICO)
        0x01, 0x00,  # 图像数量
        
        # 图像目录条目
        0x10,        # 宽度 (16)
        0x10,        # 高度 (16)
        0x00,        # 颜色数 (0 = >256色)
        0x00,        # 保留字段
        0x01, 0x00,  # 颜色平面数
        0x20, 0x00,  # 每像素位数 (32)
        0x00, 0x04, 0x00, 0x00,  # 图像数据大小
        0x16, 0x00, 0x00, 0x00,  # 图像数据偏移
        
        # 位图信息头
        0x28, 0x00, 0x00, 0x00,  # 头大小
        0x10, 0x00, 0x00, 0x00,  # 宽度
        0x20, 0x00, 0x00, 0x00,  # 高度 (包括掩码)
        0x01, 0x00,              # 平面数
        0x20, 0x00,              # 每像素位数
        0x00, 0x00, 0x00, 0x00,  # 压缩类型
        0x00, 0x04, 0x00, 0x00,  # 图像大小
        0x00, 0x00, 0x00, 0x00,  # X像素/米
        0x00, 0x00, 0x00, 0x00,  # Y像素/米
        0x00, 0x00, 0x00, 0x00,  # 使用的颜色数
        0x00, 0x00, 0x00, 0x00,  # 重要颜色数
    ])
    
    # 创建16x16的蓝色图标数据 (BGRA格式)
    blue_color = bytes([0x8A, 0x3A, 0x1E, 0xFF])  # 深蓝色 #1e3a8a
    white_color = bytes([0xFF, 0xFF, 0xFF, 0xFF])  # 白色
    
    # 创建简单的房屋图案
    icon_pixels = []
    for y in range(16):
        for x in range(16):
            # 创建简单的房屋图案
            if y < 6:  # 屋顶区域
                if abs(x - 8) <= (6 - y):
                    icon_pixels.extend(white_color)  # 屋顶
                else:
                    icon_pixels.extend(blue_color)   # 背景
            elif y < 14:  # 房屋主体
                if 3 <= x <= 12:
                    if x == 3 or x == 12 or y == 6:
                        icon_pixels.extend(white_color)  # 边框
                    elif 5 <= x <= 7 and 8 <= y <= 11:
                        icon_pixels.extend(white_color)  # 窗户1
                    elif 9 <= x <= 11 and 8 <= y <= 11:
                        icon_pixels.extend(white_color)  # 窗户2
                    elif 7 <= x <= 9 and 11 <= y <= 13:
                        icon_pixels.extend(white_color)  # 门
                    else:
                        icon_pixels.extend(blue_color)   # 房屋内部
                else:
                    icon_pixels.extend(blue_color)       # 背景
            else:  # 底部
                icon_pixels.extend(blue_color)           # 背景
    
    # 反转行顺序（BMP格式要求）
    reversed_pixels = []
    for y in range(15, -1, -1):
        start = y * 16 * 4
        end = start + 16 * 4
        reversed_pixels.extend(icon_pixels[start:end])
    
    # AND掩码（全透明）
    and_mask = bytes([0x00] * 32)  # 16x16位 = 32字节
    
    # 组合完整的ICO数据
    full_ico_data = ico_data + bytes(reversed_pixels) + and_mask
    
    return full_ico_data

def create_icon_with_pil():
    """使用PIL创建高质量图标"""
    try:
        from PIL import Image, ImageDraw
        
        # 创建多个尺寸的图标
        sizes = [(256, 256), (128, 128), (64, 64), (32, 32), (16, 16)]
        images = []
        
        for size in sizes:
            # 创建图像
            img = Image.new('RGBA', size, (30, 58, 138, 255))  # 深蓝色背景
            draw = ImageDraw.Draw(img)
            
            # 计算比例
            scale = size[0] / 256
            
            # 绘制房屋图标
            # 房屋主体
            house_left = int(64 * scale)
            house_right = int(192 * scale)
            house_top = int(128 * scale)
            house_bottom = int(224 * scale)
            draw.rectangle([house_left, house_top, house_right, house_bottom], 
                         fill=(255, 255, 255, 255))
            
            # 屋顶
            roof_peak_x = int(128 * scale)
            roof_peak_y = int(64 * scale)
            draw.polygon([(house_left, house_top), (roof_peak_x, roof_peak_y), (house_right, house_top)], 
                        fill=(220, 38, 38, 255))
            
            # 门
            door_left = int(112 * scale)
            door_right = int(144 * scale)
            door_top = int(176 * scale)
            draw.rectangle([door_left, door_top, door_right, house_bottom], 
                         fill=(139, 69, 19, 255))
            
            # 窗户
            window1_left = int(80 * scale)
            window1_right = int(112 * scale)
            window1_top = int(144 * scale)
            window1_bottom = int(176 * scale)
            draw.rectangle([window1_left, window1_top, window1_right, window1_bottom], 
                         fill=(135, 206, 235, 255))
            
            window2_left = int(144 * scale)
            window2_right = int(176 * scale)
            draw.rectangle([window2_left, window1_top, window2_right, window1_bottom], 
                         fill=(135, 206, 235, 255))
            
            images.append(img)
        
        return images
        
    except ImportError:
        return None

def main():
    """主函数"""
    print("🎨 创建房间管理系统应用图标...")
    
    icon_file = "app_icon.ico"
    
    # 尝试使用PIL创建高质量图标
    pil_images = create_icon_with_pil()
    if pil_images:
        try:
            # 保存为ICO文件
            pil_images[0].save(
                icon_file, 
                format='ICO', 
                sizes=[(img.width, img.height) for img in pil_images]
            )
            print(f"✅ 高质量图标创建成功: {icon_file}")
            return True
        except Exception as e:
            print(f"⚠️  PIL图标创建失败: {e}")
    
    # 回退到简单图标
    try:
        simple_ico_data = create_simple_icon()
        with open(icon_file, 'wb') as f:
            f.write(simple_ico_data)
        print(f"✅ 简单图标创建成功: {icon_file}")
        return True
    except Exception as e:
        print(f"❌ 图标创建失败: {e}")
        return False

if __name__ == "__main__":
    main()
