import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
from datetime import datetime, timedelta
import json
import os

class HotelManagementSystem:
    def __init__(self, root):
        self.root = root
        self.root.title("🏨 现代化房间管理系统")
        self.root.geometry("1200x800")
        self.root.configure(bg="#f8f9fa")

        # 增强的现代化配色方案 - 专业租赁管理主题
        self.colors = {
            'primary': '#1e3a8a',      # 深蓝色 - 专业可信
            'primary_light': '#3b82f6', # 亮蓝色 - 主要操作
            'secondary': '#059669',    # 翠绿色 - 成功状态
            'secondary_light': '#10b981', # 亮绿色 - 积极反馈
            'success': '#059669',      # 成功色 - 与secondary相同
            'success_light': '#10b981', # 成功悬停色
            'accent': '#7c3aed',       # 紫色 - 强调色
            'accent_light': '#8b5cf6', # 亮紫色 - 悬停状态
            'warning': '#d97706',      # 橙色 - 警告
            'warning_light': '#f59e0b', # 亮橙色 - 警告悬停
            'danger': '#dc2626',       # 红色 - 危险操作
            'danger_light': '#ef4444', # 亮红色 - 危险悬停
            'info': '#0891b2',         # 青色 - 信息提示
            'info_light': '#06b6d4',   # 亮青色 - 信息悬停
            'light': '#f8fafc',        # 极浅灰 - 背景
            'light_gray': '#f1f5f9',   # 浅灰 - 卡片背景
            'medium_gray': '#e2e8f0',  # 中灰 - 边框
            'dark_gray': '#64748b',    # 深灰 - 次要文字
            'dark': '#1e293b',         # 深色 - 主要文字
            'white': '#ffffff',        # 纯白
            'background': '#f8fafc',   # 主背景
            'card_bg': '#ffffff',      # 卡片背景
            'card_hover': '#f8fafc',   # 卡片悬停
            'text_primary': '#1e293b', # 主要文字
            'text_secondary': '#64748b', # 次要文字
            'text_muted': '#94a3b8',   # 弱化文字
            'border': '#e2e8f0',       # 主边框
            'border_light': '#f1f5f9', # 浅边框
            'shadow': 'rgba(0, 0, 0, 0.1)', # 阴影
            'shadow_hover': 'rgba(0, 0, 0, 0.15)', # 悬停阴影
            # 房间状态专用颜色
            'room_available': '#3b82f6',    # 可用房间 - 蓝色
            'room_available_hover': '#60a5fa', # 可用房间悬停
            'room_occupied': '#10b981',     # 已出租 - 绿色
            'room_occupied_hover': '#34d399', # 已出租悬停
            'room_expired': '#ef4444',      # 已过期 - 红色
            'room_expired_hover': '#f87171', # 已过期悬停
        }

        # 优化的中文字体配置
        self.fonts = {
            'title': ('Microsoft YaHei UI', 24, 'bold'),      # 主标题
            'heading': ('Microsoft YaHei UI', 16, 'bold'),    # 副标题
            'subheading': ('Microsoft YaHei UI', 14, 'bold'), # 小标题
            'body': ('Microsoft YaHei UI', 11),               # 正文
            'body_bold': ('Microsoft YaHei UI', 11, 'bold'),  # 粗体正文
            'small': ('Microsoft YaHei UI', 10),              # 小字体
            'small_bold': ('Microsoft YaHei UI', 10, 'bold'), # 小粗体
            'caption': ('Microsoft YaHei UI', 9),             # 说明文字
            'button': ('Microsoft YaHei UI', 10, 'bold'),     # 按钮文字
            'card_title': ('Microsoft YaHei UI', 12, 'bold'), # 卡片标题
            'card_text': ('Microsoft YaHei UI', 10),          # 卡片文字
            'status': ('Microsoft YaHei UI', 11, 'bold'),     # 状态文字
        }

        # 初始化数据
        self.rooms = []
        self.persons = []

        # 初始化累计财务数据
        self.total_rent_collected = 0.0  # 累计收取的租金总额
        self.total_deposit_collected = 0.0  # 累计收取的押金总额
        self.total_electricity_collected = 0.0  # 累计收取的电费总额（保留用于兼容性）

        # 初始化电表读数数据（单位：kWh）
        self.total_electricity_consumption = 0.0  # 总电量消耗（kWh）

        self.load_data()

        # 确保所有房间都有note字段
        for room in self.rooms:
            if 'note' not in room:
                room['note'] = ""

        # 创建主菜单
        self.create_menu()

        # 创建主界面
        self.create_main_interface()

        # 设置样式
        self.setup_styles()
    
    def setup_styles(self):
        """设置现代化的ttk样式主题"""
        style = ttk.Style()

        # 使用现代主题作为基础
        style.theme_use('clam')

        # === 按钮样式 ===
        # 主要按钮样式
        style.configure("Primary.TButton",
                       background=self.colors['primary'],
                       foreground=self.colors['white'],
                       font=self.fonts['button'],
                       padding=(20, 10),
                       relief="flat",
                       borderwidth=0)
        style.map("Primary.TButton",
                 background=[("active", self.colors['primary_light']),
                           ("pressed", self.colors['primary'])],
                 foreground=[("active", self.colors['white']),
                           ("pressed", self.colors['white'])])

        # 成功按钮样式
        style.configure("Success.TButton",
                       background=self.colors['secondary'],
                       foreground=self.colors['white'],
                       font=self.fonts['button'],
                       padding=(20, 10),
                       relief="flat",
                       borderwidth=0)
        style.map("Success.TButton",
                 background=[("active", self.colors['secondary_light']),
                           ("pressed", self.colors['secondary'])],
                 foreground=[("active", self.colors['white']),
                           ("pressed", self.colors['white'])])

        # 警告按钮样式
        style.configure("Warning.TButton",
                       background=self.colors['warning'],
                       foreground=self.colors['white'],
                       font=self.fonts['button'],
                       padding=(20, 10),
                       relief="flat",
                       borderwidth=0)
        style.map("Warning.TButton",
                 background=[("active", self.colors['warning_light']),
                           ("pressed", self.colors['warning'])],
                 foreground=[("active", self.colors['white']),
                           ("pressed", self.colors['white'])])

        # 危险按钮样式
        style.configure("Danger.TButton",
                       background=self.colors['danger'],
                       foreground=self.colors['white'],
                       font=self.fonts['button'],
                       padding=(20, 10),
                       relief="flat",
                       borderwidth=0)
        style.map("Danger.TButton",
                 background=[("active", self.colors['danger_light']),
                           ("pressed", self.colors['danger'])],
                 foreground=[("active", self.colors['white']),
                           ("pressed", self.colors['white'])])

        # === 输入框样式 ===
        style.configure("Modern.TEntry",
                       fieldbackground=self.colors['white'],
                       bordercolor=self.colors['border'],
                       lightcolor=self.colors['border_light'],
                       darkcolor=self.colors['border'],
                       borderwidth=1,
                       relief="solid",
                       padding=(10, 8),
                       font=self.fonts['body'])
        style.map("Modern.TEntry",
                 bordercolor=[("focus", self.colors['primary']),
                            ("active", self.colors['primary_light'])],
                 lightcolor=[("focus", self.colors['primary']),
                           ("active", self.colors['primary_light'])])

        # === 标签样式 ===
        style.configure("Heading.TLabel",
                       background=self.colors['background'],
                       foreground=self.colors['text_primary'],
                       font=self.fonts['heading'])

        style.configure("Body.TLabel",
                       background=self.colors['background'],
                       foreground=self.colors['text_primary'],
                       font=self.fonts['body'])

        style.configure("Caption.TLabel",
                       background=self.colors['background'],
                       foreground=self.colors['text_secondary'],
                       font=self.fonts['caption'])

        # === 框架样式 ===
        style.configure("Card.TFrame",
                       background=self.colors['card_bg'],
                       relief="flat",
                       borderwidth=1,
                       lightcolor=self.colors['border_light'],
                       darkcolor=self.colors['border'])

        # === 表格相关样式 ===
        style.configure("TableContainer.TFrame",
                       borderwidth=1,
                       relief="solid",
                       background=self.colors['card_bg'])
        style.configure("Table.TLabel",
                       padding=8,
                       background=self.colors['card_bg'],
                       font=self.fonts['body'])
        style.configure("Table.TCheckbutton",
                       padding=8,
                       background=self.colors['card_bg'],
                       font=self.fonts['body'])

        # === 下拉框样式 ===
        style.configure("Modern.TCombobox",
                       fieldbackground=self.colors['white'],
                       bordercolor=self.colors['border'],
                       arrowcolor=self.colors['text_secondary'],
                       font=self.fonts['body'])

        # === Notebook样式 ===
        style.configure("Modern.TNotebook",
                       background=self.colors['background'],
                       borderwidth=0)
        style.configure("Modern.TNotebook.Tab",
                       background=self.colors['light_gray'],
                       foreground=self.colors['text_primary'],
                       font=self.fonts['body_bold'],
                       padding=(20, 10))
        style.map("Modern.TNotebook.Tab",
                 background=[("selected", self.colors['white']),
                           ("active", self.colors['card_hover'])],
                 foreground=[("selected", self.colors['primary']),
                           ("active", self.colors['text_primary'])])
    
    def create_menu(self):
        menubar = tk.Menu(self.root)
        
        # 房屋管理菜单
        room_menu = tk.Menu(menubar, tearoff=0)
        room_menu.add_command(label="添加房间", command=self.add_room)
        room_menu.add_command(label="查看所有房间", command=self.show_all_rooms)
        menubar.add_cascade(label="房间管理", menu=room_menu)
        
        # 人员管理菜单
        person_menu = tk.Menu(menubar, tearoff=0)
        person_menu.add_command(label="添加人员", command=self.add_person)
        person_menu.add_command(label="查看所有人员", command=self.show_all_persons)
        menubar.add_cascade(label="人员管理", menu=person_menu)
        
        self.root.config(menu=menubar)
    
    def create_main_interface(self):
        # 主框架 - 使用现代化背景色
        main_frame = tk.Frame(self.root, bg=self.colors['background'])
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # 现代化标题区域
        title_frame = tk.Frame(main_frame, bg=self.colors['background'])
        title_frame.pack(fill="x", pady=(0, 20))

        # 主标题
        title_label = tk.Label(title_frame,
                             text="🏠 房间管理系统",
                             font=self.fonts['title'],
                             bg=self.colors['background'],
                             fg=self.colors['primary'])
        title_label.pack(side="left")

        # 副标题
        subtitle_label = tk.Label(title_frame,
                                text="现代化租赁管理平台",
                                font=self.fonts['caption'],
                                bg=self.colors['background'],
                                fg=self.colors['text_secondary'])
        subtitle_label.pack(side="left", padx=(15, 0), pady=(8, 0))
        
        # 卡片容器（带现代化滚动条）
        container_frame = tk.Frame(main_frame, bg=self.colors['background'])
        container_frame.pack(fill="both", expand=True)

        # 创建Canvas和滚动条
        self.canvas = tk.Canvas(container_frame,
                               bg=self.colors['background'],
                               highlightthickness=0,
                               relief="flat",
                               bd=0)

        # 创建现代化滚动条样式
        style = ttk.Style()
        style.configure("MainView.Vertical.TScrollbar",
                       background=self.colors['medium_gray'],
                       troughcolor=self.colors['light_gray'],
                       bordercolor=self.colors['border'],
                       arrowcolor=self.colors['text_secondary'],
                       darkcolor=self.colors['border'],
                       lightcolor=self.colors['white'],
                       borderwidth=0,
                       relief="flat")

        # 创建滚动条
        self.scrollbar = ttk.Scrollbar(container_frame,
                                      orient="vertical",
                                      command=self.canvas.yview,
                                      style="MainView.Vertical.TScrollbar")

        # 创建卡片容器
        self.card_container = tk.Frame(self.canvas, bg=self.colors['background'])

        # 配置滚动区域
        def configure_scroll_region(event=None):
            self.canvas.configure(scrollregion=self.canvas.bbox("all"))
            # 检查是否需要显示滚动条
            self.update_scrollbar_visibility()

        self.card_container.bind("<Configure>", configure_scroll_region)

        # 创建窗口
        self.canvas_window = self.canvas.create_window((0, 0), window=self.card_container, anchor="nw")
        self.canvas.configure(yscrollcommand=self.scrollbar.set)

        # 绑定Canvas大小变化事件，确保内容宽度适应
        def configure_canvas(event):
            canvas_width = event.width
            self.canvas.itemconfig(self.canvas_window, width=canvas_width)
            self.update_scrollbar_visibility()

        self.canvas.bind('<Configure>', configure_canvas)

        # 改进的鼠标滚轮事件处理
        def _on_mousewheel(event):
            # 检查是否有内容需要滚动
            if self.canvas.winfo_exists():
                bbox = self.canvas.bbox("all")
                if bbox and bbox[3] > self.canvas.winfo_height():
                    # 平滑滚动
                    self.canvas.yview_scroll(int(-1*(event.delta/120)), "units")

        # 跨平台兼容的滚轮事件绑定
        def bind_mousewheel():
            if self.root.tk.call("tk", "windowingsystem") == "aqua":
                # macOS
                self.canvas.bind("<Button-2>", _on_mousewheel)
                self.canvas.bind("<B2-Motion>", _on_mousewheel)
            else:
                # Windows 和 Linux
                self.canvas.bind("<MouseWheel>", _on_mousewheel)
                # Linux 额外支持
                self.canvas.bind("<Button-4>", lambda e: _on_mousewheel(type('obj', (object,), {'delta': 120})()))
                self.canvas.bind("<Button-5>", lambda e: _on_mousewheel(type('obj', (object,), {'delta': -120})()))

        # 鼠标进入和离开事件处理
        def on_enter(event):
            bind_mousewheel()
            # 绑定到整个应用程序
            self.root.bind_all("<MouseWheel>", _on_mousewheel)

        def on_leave(event):
            # 解绑全局滚轮事件
            self.root.unbind_all("<MouseWheel>")

        # 绑定鼠标进入和离开事件
        self.canvas.bind("<Enter>", on_enter)
        self.canvas.bind("<Leave>", on_leave)
        self.card_container.bind("<Enter>", on_enter)
        self.card_container.bind("<Leave>", on_leave)

        # 布局Canvas和滚动条
        self.canvas.pack(side="left", fill="both", expand=True)
        self.scrollbar.pack(side="right", fill="y")

        # 初始化时隐藏滚动条
        self.scrollbar.pack_forget()

        # 创建状态栏
        self.create_status_bar()

        # 刷新卡片
        self.refresh_cards()

    def update_scrollbar_visibility(self):
        """根据内容高度动态显示或隐藏滚动条"""
        try:
            # 更新画布以获取准确的边界框
            self.canvas.update_idletasks()

            # 获取内容的边界框
            bbox = self.canvas.bbox("all")
            if bbox:
                content_height = bbox[3] - bbox[1]  # 内容高度
                canvas_height = self.canvas.winfo_height()  # 画布高度

                # 如果内容高度大于画布高度，显示滚动条
                if content_height > canvas_height and canvas_height > 1:
                    if not self.scrollbar.winfo_viewable():
                        self.scrollbar.pack(side="right", fill="y")
                else:
                    # 内容适合显示，隐藏滚动条
                    if self.scrollbar.winfo_viewable():
                        self.scrollbar.pack_forget()
            else:
                # 没有内容时隐藏滚动条
                if self.scrollbar.winfo_viewable():
                    self.scrollbar.pack_forget()
        except Exception as e:
            # 如果出现错误，保持滚动条显示以确保功能正常
            print(f"滚动条可见性更新错误: {e}")
            if not self.scrollbar.winfo_viewable():
                self.scrollbar.pack(side="right", fill="y")

    def create_status_bar(self):
        """创建现代化状态栏"""
        # 状态栏主框架 - 现代化设计，进一步增加高度以确保按钮完整显示
        self.status_frame = tk.Frame(self.root,
                                   bg=self.colors['white'],
                                   relief="flat",
                                   bd=0,
                                   height=140)  # 进一步增加高度从130到140，确保按钮完整显示
        self.status_frame.pack(side="bottom", fill="x", padx=20, pady=(0, 10))
        self.status_frame.pack_propagate(False)  # 保持固定高度

        # 添加现代化顶部分隔线 - 渐变效果
        separator_frame = tk.Frame(self.status_frame, height=3, bg=self.colors['white'])
        separator_frame.pack(fill="x")

        top_line = tk.Frame(separator_frame, height=1, bg=self.colors['primary'])
        top_line.pack(fill="x")

        gradient_line = tk.Frame(separator_frame, height=2, bg=self.colors['border_light'])
        gradient_line.pack(fill="x")

        # 状态栏内容框架 - 填充布局
        content_frame = tk.Frame(self.status_frame, bg=self.colors['white'])
        content_frame.pack(fill="both", expand=True, padx=30, pady=15)

        # 卡片容器 - 填充整个宽度，均匀分布
        cards_container = tk.Frame(content_frame, bg=self.colors['white'])
        cards_container.pack(fill="x", expand=True)

        # 响应式状态卡片容器 - 优化布局和按钮设计
        def create_status_card(parent, icon, title, value, color, reset_command=None, card_width=None):
            """创建响应式状态卡片，优化按钮设计"""
            # 主卡片框架 - 进一步增加高度以确保按钮完整显示
            card_frame = tk.Frame(parent,
                                bg=self.colors['white'],  # 恢复白色背景
                                relief="solid",
                                bd=1,  # 恢复原始边框宽度
                                height=110)  # 进一步增加高度从100到110，确保按钮有足够空间
            card_frame.pack(side="left", padx=4, pady=8, fill="both", expand=True)
            card_frame.pack_propagate(False)  # 保持固定高度

            # 内容容器 - 减少内边距为按钮留出更多空间
            content_frame = tk.Frame(card_frame, bg=self.colors['white'])
            content_frame.pack(fill="both", expand=True, padx=8, pady=6)  # 减少内边距

            # 标题行（图标 + 标题）- 减少高度为按钮留空间
            title_frame = tk.Frame(content_frame, bg=self.colors['white'], height=20)
            title_frame.pack(fill="x")
            title_frame.pack_propagate(False)

            # 图标 - 优化样式
            icon_label = tk.Label(title_frame,
                                text=icon,
                                bg=self.colors['white'],
                                font=("Segoe UI Emoji", 11),  # 稍微减小图标字体
                                width=2)
            icon_label.pack(side="left", anchor="w")

            # 标题 - 优化字体
            title_label = tk.Label(title_frame,
                                 text=title,
                                 bg=self.colors['white'],
                                 fg=self.colors['text_secondary'],
                                 font=("Microsoft YaHei UI", 8))  # 减小标题字体
            title_label.pack(side="left", padx=(3, 0), anchor="w")

            # 数值显示区域 - 进一步减少高度为按钮留出更多空间
            value_frame = tk.Frame(content_frame, bg=self.colors['white'], height=22)
            value_frame.pack(fill="x", pady=(1, 0))
            value_frame.pack_propagate(False)

            value_label = tk.Label(value_frame,
                                 text=value,
                                 bg=self.colors['white'],
                                 fg=color,
                                 font=("Microsoft YaHei UI", 10, "bold"))  # 进一步减小字体为按钮留空间
            value_label.pack(anchor="w")

            # 按钮区域 - 确保有足够空间完整显示按钮
            if reset_command:
                # 按钮容器，使用剩余空间
                button_frame = tk.Frame(content_frame, bg=self.colors['white'])
                button_frame.pack(fill="both", expand=True, pady=(3, 0))

                reset_btn = tk.Button(button_frame,
                                    text="🔄 重置",
                                    font=("Microsoft YaHei UI", 8, "bold"),
                                    bg=self.colors['warning'],
                                    fg=self.colors['white'],
                                    relief="flat",
                                    bd=0,
                                    padx=6,   # 优化内边距
                                    pady=3,   # 稍微增加垂直内边距
                                    command=reset_command,
                                    cursor="hand2",
                                    activebackground=self.colors['warning_light'])
                reset_btn.pack(side="right", anchor="se", padx=(0, 2), pady=(0, 2))  # 定位到右下角

                # 增强按钮悬停效果
                def on_btn_enter(e):
                    reset_btn.configure(bg=self.colors['warning_light'])
                def on_btn_leave(e):
                    reset_btn.configure(bg=self.colors['warning'])

                reset_btn.bind("<Enter>", on_btn_enter)
                reset_btn.bind("<Leave>", on_btn_leave)

            return value_label

        # 租金总额卡片 - 响应式尺寸
        self.rent_total_label = create_status_card(cards_container,
                                                  "💰",
                                                  "租金总额",
                                                  f"¥{self.total_rent_collected:.0f}",
                                                  self.colors['secondary'],
                                                  self.reset_rent_total)

        # 押金总额卡片 - 响应式尺寸
        self.deposit_total_label = create_status_card(cards_container,
                                                     "🏦",
                                                     "押金总额",
                                                     f"¥{self.total_deposit_collected:.0f}",
                                                     self.colors['info'],
                                                     self.reset_deposit_total)

        # 响应式电量管理卡片
        electricity_card = tk.Frame(cards_container,
                                  bg=self.colors['white'],
                                  relief="solid",
                                  bd=1,
                                  height=110)  # 与其他卡片保持一致的高度
        electricity_card.pack(side="left", padx=4, pady=8, fill="both", expand=True)
        electricity_card.pack_propagate(False)

        # 内容容器
        elec_content = tk.Frame(electricity_card, bg=self.colors['white'])
        elec_content.pack(fill="both", expand=True, padx=8, pady=6)

        # 标题行 - 统一高度
        elec_title_frame = tk.Frame(elec_content, bg=self.colors['white'], height=20)
        elec_title_frame.pack(fill="x")
        elec_title_frame.pack_propagate(False)

        elec_icon = tk.Label(elec_title_frame,
                           text="⚡",
                           bg=self.colors['white'],
                           font=("Segoe UI Emoji", 11),
                           width=2)
        elec_icon.pack(side="left", anchor="w")

        elec_title = tk.Label(elec_title_frame,
                            text="总电量",
                            bg=self.colors['white'],
                            fg=self.colors['text_secondary'],
                            font=("Microsoft YaHei UI", 8))
        elec_title.pack(side="left", padx=(3, 0), anchor="w")

        # 输入区域 - 统一高度
        input_frame = tk.Frame(elec_content, bg=self.colors['white'], height=25)
        input_frame.pack(fill="x", pady=(2, 0))
        input_frame.pack_propagate(False)

        input_container = tk.Frame(input_frame, bg=self.colors['white'])
        input_container.pack(anchor="w")

        self.electricity_input = tk.Entry(input_container,
                                        width=12,
                                        font=("Microsoft YaHei UI", 9, "bold"),
                                        bg=self.colors['white'],
                                        fg=self.colors['warning'],
                                        relief="solid",
                                        bd=1,
                                        justify="center")
        self.electricity_input.pack(side="left")
        self.electricity_input.insert(0, str(int(self.total_electricity_consumption)))

        kwh_label = tk.Label(input_container,
                           text=" kWh",
                           bg=self.colors['white'],
                           fg=self.colors['warning'],
                           font=("Microsoft YaHei UI", 8))
        kwh_label.pack(side="left", padx=(2, 0))

        # 按钮区域 - 与重置按钮保持完全一致的样式和布局
        button_frame = tk.Frame(elec_content, bg=self.colors['white'])
        button_frame.pack(fill="both", expand=True, pady=(3, 0))

        save_btn = tk.Button(button_frame,
                           text="💾 保存",  # 添加图标
                           font=("Microsoft YaHei UI", 8, "bold"),  # 与重置按钮相同的字体大小
                           bg=self.colors['secondary'],
                           fg=self.colors['white'],
                           relief="flat",
                           bd=0,
                           padx=6,   # 与重置按钮相同的水平内边距
                           pady=3,   # 与重置按钮相同的垂直内边距
                           command=self.save_electricity_total,
                           cursor="hand2",
                           activebackground=self.colors['primary'])  # 添加悬停效果
        save_btn.pack(side="right", anchor="se", padx=(0, 2), pady=(0, 2))  # 与重置按钮相同的定位

        # 增强保存按钮悬停效果
        def on_save_btn_enter(e):
            save_btn.configure(bg=self.colors['primary'])
        def on_save_btn_leave(e):
            save_btn.configure(bg=self.colors['secondary'])

        save_btn.bind("<Enter>", on_save_btn_enter)
        save_btn.bind("<Leave>", on_save_btn_leave)

        # 响应式到期房间状态卡片
        expired_card = tk.Frame(cards_container,
                              bg=self.colors['white'],
                              relief="solid",
                              bd=1,
                              height=110)  # 与其他卡片保持一致的高度
        expired_card.pack(side="left", padx=4, pady=8, fill="both", expand=True)
        expired_card.pack_propagate(False)

        # 内容容器
        expired_content = tk.Frame(expired_card, bg=self.colors['white'])
        expired_content.pack(fill="both", expand=True, padx=8, pady=6)

        # 标题行 - 统一高度
        expired_title_frame = tk.Frame(expired_content, bg=self.colors['white'], height=20)
        expired_title_frame.pack(fill="x")
        expired_title_frame.pack_propagate(False)

        expired_icon = tk.Label(expired_title_frame,
                              text="⚠️",
                              bg=self.colors['white'],
                              font=("Segoe UI Emoji", 11),
                              width=2)
        expired_icon.pack(side="left", anchor="w")

        expired_title = tk.Label(expired_title_frame,
                               text="到期提醒",
                               bg=self.colors['white'],
                               fg=self.colors['text_secondary'],
                               font=("Microsoft YaHei UI", 8))
        expired_title.pack(side="left", padx=(3, 0), anchor="w")

        # 计算初始到期房间数量
        initial_expired_count = 0
        today = datetime.today().date()
        for room in self.rooms:
            if room["status"] == "已出租" and room.get("end_date"):
                try:
                    end_date = datetime.strptime(room["end_date"], "%Y-%m-%d").date()
                    if end_date < today:
                        initial_expired_count += 1
                except ValueError:
                    pass

        # 数值显示区域 - 统一高度
        value_frame = tk.Frame(expired_content, bg=self.colors['white'], height=25)
        value_frame.pack(fill="x", pady=(2, 0))
        value_frame.pack_propagate(False)

        # 到期房间数量 - 显示实际数据
        initial_expired_text = f"{initial_expired_count}间" if initial_expired_count > 0 else "0间"
        initial_expired_color = self.colors['danger'] if initial_expired_count > 0 else self.colors['text_secondary']

        self.expired_rooms_label = tk.Label(value_frame,
                                          text=initial_expired_text,
                                          bg=self.colors['white'],
                                          fg=initial_expired_color,
                                          font=("Microsoft YaHei UI", 11, "bold"))
        self.expired_rooms_label.pack(anchor="w")

        # 更新状态栏（确保所有数据都是最新的）
        self.update_status_bar()

    def update_status_bar(self):
        """更新状态栏信息"""
        expired_count = 0
        today = datetime.today().date()

        # 计算到期房间数量
        for room in self.rooms:
            if room["status"] == "已出租" and room.get("end_date"):
                try:
                    end_date = datetime.strptime(room["end_date"], "%Y-%m-%d").date()
                    if end_date < today:
                        expired_count += 1
                except ValueError:
                    pass

        # 更新现代化状态卡片 - 显示累计财务数据
        self.rent_total_label.config(text=f"¥{self.total_rent_collected:.0f}")
        self.deposit_total_label.config(text=f"¥{self.total_deposit_collected:.0f}")

        # 更新总电量输入框
        if hasattr(self, 'electricity_input'):
            current_value = self.electricity_input.get()
            new_value = str(int(self.total_electricity_consumption))
            if current_value != new_value:
                self.electricity_input.delete(0, tk.END)
                self.electricity_input.insert(0, new_value)

        # 更新到期房间状态
        if expired_count > 0:
            self.expired_rooms_label.config(text=f"{expired_count}间",
                                          fg=self.colors['danger'])
        else:
            self.expired_rooms_label.config(text="0间",
                                          fg=self.colors['text_secondary'])

    def reset_rent_total(self):
        """重置租金总额"""
        try:
            result = messagebox.askyesno("⚠️ 确认操作",
                                       "确定要将租金总额归零吗？\n\n此操作不可撤销！",
                                       icon="warning")
            if result:
                self.total_rent_collected = 0.0
                self.save_data()
                self.update_status_bar()
                messagebox.showinfo("✅ 操作成功", "租金总额已归零")
        except Exception as e:
            messagebox.showerror("错误", f"操作失败: {e}")

    def reset_deposit_total(self):
        """重置押金总额"""
        try:
            result = messagebox.askyesno("⚠️ 确认操作",
                                       "确定要将押金总额归零吗？\n\n此操作不可撤销！",
                                       icon="warning")
            if result:
                self.total_deposit_collected = 0.0
                self.save_data()
                self.update_status_bar()
                messagebox.showinfo("✅ 操作成功", "押金总额已归零")
        except Exception as e:
            messagebox.showerror("错误", f"操作失败: {e}")



    def save_electricity_total(self):
        """保存总电量输入值"""
        try:
            # 获取输入值并转换为整数
            input_value = self.electricity_input.get().strip()
            if not input_value:
                messagebox.showwarning("⚠️ 警告", "请输入总电量值")
                return

            # 验证输入是否为有效的整数
            try:
                electricity_value = int(float(input_value))  # 先转float再转int，处理小数输入
            except ValueError:
                messagebox.showerror("❌ 错误", "请输入有效的数字")
                return

            if electricity_value < 0:
                messagebox.showerror("❌ 错误", "总电量不能为负数")
                return

            # 更新总电量消耗值
            self.total_electricity_consumption = float(electricity_value)

            # 更新输入框显示为整数
            self.electricity_input.delete(0, tk.END)
            self.electricity_input.insert(0, str(electricity_value))

            # 保存数据
            self.save_data()

            messagebox.showinfo("✅ 保存成功", f"总电量已更新为 {electricity_value} kWh")

        except Exception as e:
            messagebox.showerror("❌ 错误", f"保存失败: {e}")



    def show_multi_function_window(self, room):
        """显示多功能窗口"""
        try:
            print(f"尝试打开房间管理窗口: {room['room_number']}")  # 调试输出
            if not hasattr(self, 'multi_func_window') or not self.multi_func_window.winfo_exists():
                self.multi_func_window = MultiFunctionWindow(self.root, self, room)
                print("窗口创建成功")
            else:
                self.multi_func_window.lift()
                print("窗口已存在，置顶显示")
        except Exception as e:
            print(f"打开窗口失败: {str(e)}")
            messagebox.showerror("错误", f"无法打开管理窗口: {str(e)}")

    def refresh_cards(self):
        # 清空现有卡片
        for widget in self.card_container.winfo_children():
            widget.destroy()

        # 更新状态栏
        self.update_status_bar()
        
        # 创建新的卡片
        for i, room in enumerate(self.rooms):
            # 根据房间状态设置现代化配色
            today = datetime.today().date()
            end_date = datetime.strptime(room["end_date"], "%Y-%m-%d").date() if room.get("end_date") else None

            # 使用新的现代化配色方案
            if room["status"] == "已出租":
                if end_date and end_date < today:
                    bg_color = self.colors['room_expired']  # 已过期 - 红色
                    text_color = self.colors['white']
                    status_icon = "⚠️"
                else:
                    bg_color = self.colors['room_occupied']  # 已出租 - 绿色
                    text_color = self.colors['white']
                    status_icon = "✅"
            else:
                bg_color = self.colors['room_available']  # 闲置 - 蓝色
                text_color = self.colors['white']
                status_icon = "🏠"

            # 创建现代化卡片 - 增强视觉效果
            card = tk.Frame(self.card_container,
                           width=240, height=320,
                           bg=bg_color,
                           relief="flat",
                           bd=0,
                           highlightthickness=1,
                           highlightcolor=self.colors['border'],
                           highlightbackground=self.colors['border'])
            # 现代化网格布局 - 响应式设计
            columns = 4  # 每行显示4个卡片
            row = i // columns
            col = i % columns

            # 使用更大的间距和现代化布局
            card.grid(row=row, column=col,
                     padx=20, pady=20,
                     sticky="nsew",
                     ipadx=5, ipady=5)
            card.pack_propagate(False)

            # 配置网格权重，确保卡片均匀分布和响应式布局
            self.card_container.grid_columnconfigure(col, weight=1, minsize=260)
            self.card_container.grid_rowconfigure(row, weight=0, minsize=340)

            # 创建现代化卡片内容容器
            text_frame = tk.Frame(card, bg=bg_color)
            text_frame.pack(expand=True, fill="both", padx=15, pady=15)

            # 卡片顶部 - 房间号和状态图标
            header_frame = tk.Frame(text_frame, bg=bg_color)
            header_frame.pack(fill="x", pady=(0, 10))

            # 状态图标
            icon_label = tk.Label(header_frame,
                                text=status_icon,
                                bg=bg_color,
                                font=("Segoe UI Emoji", 16))
            icon_label.pack(side="left")

            # 房间号
            room_number_label = tk.Label(header_frame,
                                       text=f"房间 {room['room_number']}",
                                       bg=bg_color,
                                       fg=text_color,
                                       font=self.fonts['card_title'])
            room_number_label.pack(side="right")

            # 分隔线
            separator = tk.Frame(text_frame, height=1, bg=text_color)
            separator.pack(fill="x", pady=(0, 10))

            # 房间状态
            status_text = "闲置"  # 默认
            if room["status"] == "已出租":
                if end_date and end_date < today:
                    status_text = "已到期"
                else:
                    status_text = "租赁中"

            status_label = tk.Label(text_frame,
                                  text=f"状态：{status_text}",
                                  bg=bg_color,
                                  fg=text_color,
                                  font=self.fonts['body_bold'])
            status_label.pack(anchor="w", pady=2)

            # 租户信息（仅在已出租时显示）
            if room["status"] == "已出租":
                # 获取所有租户信息
                tenant_names = []
                for tenant_id in room.get("tenant_ids", []):
                    tenant = next((p for p in self.persons if p["id"] == tenant_id), None)
                    if tenant:
                        tenant_names.append(tenant.get('name', ''))

                tenant_display = ", ".join(tenant_names) if tenant_names else "未知租户"

                tenant_label = tk.Label(text_frame,
                                      text=f"👤 {tenant_display}",
                                      bg=bg_color,
                                      fg=text_color,
                                      font=self.fonts['small_bold'])
                tenant_label.pack(anchor="w", pady=2)

            # 财务信息区域
            finance_frame = tk.Frame(text_frame, bg=bg_color)
            finance_frame.pack(fill="x", pady=(5, 0))

            # 租金信息 - 添加中文标签
            rent_label = tk.Label(finance_frame,
                                text=f"💰 租金: ¥{room['rent']:.0f}",
                                bg=bg_color,
                                fg=text_color,
                                font=self.fonts['small'])
            rent_label.pack(side="left")

            # 押金信息 - 添加中文标签
            deposit_label = tk.Label(finance_frame,
                                   text=f"🏦 押金: ¥{room['deposit']:.0f}",
                                   bg=bg_color,
                                   fg=text_color,
                                   font=self.fonts['small'])
            deposit_label.pack(side="right")

            # 电表信息 - 使用正确的中文标签和单位
            electricity_label = tk.Label(text_frame,
                                        text=f"⚡ 电表: {int(room.get('electricity_fee', 0))} kWh",
                                        bg=bg_color,
                                        fg=text_color,
                                        font=self.fonts['small'])
            electricity_label.pack(anchor="w", pady=2)

            # 日期信息（仅在已出租时显示）
            if room["status"] == "已出租":
                date_frame = tk.Frame(text_frame, bg=bg_color)
                date_frame.pack(fill="x", pady=(5, 0))

                start_date_label = tk.Label(date_frame,
                                          text=f"📅 {room.get('start_date', '')}",
                                          bg=bg_color,
                                          fg=text_color,
                                          font=self.fonts['caption'])
                start_date_label.pack(anchor="w")

                end_date_label = tk.Label(date_frame,
                                        text=f"📆 {room.get('end_date', '')}",
                                        bg=bg_color,
                                        fg=text_color,
                                        font=self.fonts['caption'])
                end_date_label.pack(anchor="w")
            
            # 存储文字容器和标签的引用，以便在悬停时更新背景色
            card.text_frame = text_frame
            card.labels = text_frame.winfo_children()
            
            # 现代化鼠标悬停效果
            def on_enter(e, r=room):
                # 获取悬停颜色
                today = datetime.today().date()
                end_date = datetime.strptime(r["end_date"], "%Y-%m-%d").date() if r.get("end_date") else None

                if r["status"] == "已出租":
                    if end_date and end_date < today:
                        hover_bg = self.colors['room_expired_hover']  # 已过期悬停
                    else:
                        hover_bg = self.colors['room_occupied_hover']  # 已出租悬停
                else:
                    hover_bg = self.colors['room_available_hover']  # 闲置悬停

                # 更新卡片和所有子组件的背景色
                def update_widget_bg(widget, bg_color):
                    try:
                        widget.configure(bg=bg_color)
                        for child in widget.winfo_children():
                            update_widget_bg(child, bg_color)
                    except tk.TclError:
                        pass  # 忽略不支持背景色的组件

                update_widget_bg(e.widget, hover_bg)

                # 添加轻微的阴影效果（通过边框模拟）
                e.widget.configure(highlightthickness=2,
                                 highlightcolor=self.colors['primary_light'])

            def on_leave(e, r=room):
                # 获取原始颜色
                today = datetime.today().date()
                end_date = datetime.strptime(r["end_date"], "%Y-%m-%d").date() if r.get("end_date") else None

                if r["status"] == "已出租":
                    if end_date and end_date < today:
                        original_bg = self.colors['room_expired']  # 已过期
                    else:
                        original_bg = self.colors['room_occupied']  # 已出租
                else:
                    original_bg = self.colors['room_available']  # 闲置

                # 恢复卡片和所有子组件的背景色
                def update_widget_bg(widget, bg_color):
                    try:
                        widget.configure(bg=bg_color)
                        for child in widget.winfo_children():
                            update_widget_bg(child, bg_color)
                    except tk.TclError:
                        pass  # 忽略不支持背景色的组件

                update_widget_bg(e.widget, original_bg)

                # 恢复边框
                e.widget.configure(highlightthickness=1,
                                 highlightcolor=self.colors['border'])
            
            card.bind("<Enter>", on_enter)
            card.bind("<Leave>", on_leave)
            
            # 增强左键点击事件绑定
            def handle_click(e, r=room):
                print(f"点击检测到: {r['room_number']}")  # 调试输出
                self.show_multi_function_window(r)
            
            card.bind("<Button-1>", handle_click)
            # 为卡片内所有子组件绑定左键事件，确保事件传播
            for child in card.winfo_children():
                child.bind("<Button-1>", handle_click)

            # 添加右键菜单功能
            def handle_right_click(e, r=room):
                self.show_room_context_menu(e, r)

            card.bind("<Button-3>", handle_right_click)
            # 为卡片内所有子组件绑定右键事件
            for child in card.winfo_children():
                child.bind("<Button-3>", handle_right_click)
                for grandchild in child.winfo_children():
                    grandchild.bind("<Button-3>", handle_right_click)

        # 刷新卡片后更新滚动条可见性
        self.root.after(100, self.update_scrollbar_visibility)  # 延迟更新以确保布局完成
    
    def show_room_context_menu(self, event, room):
        """显示现代化的房间右键上下文菜单"""
        menu = tk.Menu(self.root, tearoff=0,
                      bg=self.colors['white'],
                      fg=self.colors['text_primary'],
                      activebackground=self.colors['secondary'],
                      activeforeground=self.colors['white'],
                      font=('Segoe UI', 10))

        # 根据房间状态动态添加菜单项
        if room["status"] == "已出租":
            # 已出租房间的菜单选项
            menu.add_command(label="🔄 续租",
                           command=lambda: self.show_extend_rent_dialog(room),
                           font=('Segoe UI', 10))
            menu.add_command(label="🚪 退房",
                           command=lambda: self.show_checkout_dialog(room),
                           font=('Segoe UI', 10))
            menu.add_separator()

        # 所有房间都有的菜单选项
        menu.add_command(label="📝 编辑备注",
                        command=lambda: self.show_edit_note_dialog(room),
                        font=('Segoe UI', 10))
        menu.add_command(label="📊 房间信息总览",
                        command=lambda: self.show_room_history_dialog(room),
                        font=('Segoe UI', 10))

        try:
            menu.tk_popup(event.x_root, event.y_root)
        finally:
            menu.grab_release()

    def show_extend_rent_dialog(self, room):
        """显示续租对话框"""
        if room["status"] != "已出租":
            messagebox.showwarning("⚠️ 警告", "只有已出租的房间才能续租")
            return

        # 直接调用现有的续租功能
        self.extend_rent(room)

    def show_checkout_dialog(self, room):
        """现代化的退房确认对话框"""
        if room["status"] != "已出租":
            messagebox.showwarning("⚠️ 警告", "只有已出租的房间才能退房")
            return

        # 创建现代化退房确认对话框 - 增加高度确保按钮可见
        checkout_window = tk.Toplevel(self.root)
        checkout_window.title("🚪 确认退房")
        checkout_window.geometry("550x650")  # 进一步增加尺寸
        checkout_window.configure(bg=self.colors['background'])
        checkout_window.resizable(True, True)  # 允许调整大小
        checkout_window.minsize(550, 500)  # 设置最小尺寸
        checkout_window.transient(self.root)
        checkout_window.grab_set()

        # 窗口居中
        checkout_window.update_idletasks()
        width = 550
        height = 650
        x = (checkout_window.winfo_screenwidth() // 2) - (width // 2)
        y = (checkout_window.winfo_screenheight() // 2) - (height // 2)
        checkout_window.geometry(f"{width}x{height}+{x}+{y}")

        # 主容器 - 使用更好的布局管理
        main_frame = tk.Frame(checkout_window, bg=self.colors['background'])
        main_frame.pack(fill="both", expand=True, padx=30, pady=20)

        # 内容区域（为按钮预留空间）
        content_frame = tk.Frame(main_frame, bg=self.colors['background'])
        content_frame.pack(fill="both", expand=True, pady=(0, 80))  # 为按钮预留80px空间

        # 按钮区域（固定在底部）
        button_container = tk.Frame(main_frame, bg=self.colors['background'], height=70)
        button_container.pack(side="bottom", fill="x")
        button_container.pack_propagate(False)

        # 警告图标和标题
        title_frame = tk.Frame(content_frame, bg=self.colors['background'])
        title_frame.pack(fill="x", pady=(0, 20))

        warning_icon = tk.Label(title_frame,
                              text="⚠️",
                              font=("Segoe UI Emoji", 28),  # 稍微减小图标
                              bg=self.colors['background'],
                              fg=self.colors['warning'])
        warning_icon.pack()

        title_label = tk.Label(title_frame,
                             text="确认退房",
                             font=self.fonts['heading'],
                             bg=self.colors['background'],
                             fg=self.colors['danger'])
        title_label.pack(pady=(8, 0))

        subtitle_label = tk.Label(title_frame,
                                text=f"房间 {room['room_number']} 退房确认",
                                font=self.fonts['caption'],
                                bg=self.colors['background'],
                                fg=self.colors['text_secondary'])
        subtitle_label.pack(pady=(5, 0))

        # 房间信息卡片
        info_card = tk.Frame(content_frame,
                           bg=self.colors['card_bg'],
                           relief="flat",
                           bd=1,
                           highlightthickness=1,
                           highlightcolor=self.colors['border'],
                           highlightbackground=self.colors['border'])
        info_card.pack(fill="x", pady=(0, 15))

        # 获取租户信息
        tenant_names = []
        for tenant_id in room.get("tenant_ids", []):
            tenant = next((p for p in self.persons if p["id"] == tenant_id), None)
            if tenant:
                tenant_names.append(tenant['name'])

        tenant_display = ", ".join(tenant_names) if tenant_names else "未知租户"

        # 卡片内容
        info_content = tk.Frame(info_card, bg=self.colors['card_bg'])
        info_content.pack(fill="x", padx=20, pady=20)

        # 房间信息
        info_data = [
            ("房号:", room['room_number']),
            ("租户:", tenant_display),
            ("押金:", f"¥{room['deposit']:.0f}"),
            ("起租日期:", room.get('start_date', '未知')),
            ("截止日期:", room.get('end_date', '未知'))
        ]

        for i, (label_text, value_text) in enumerate(info_data):
            self.create_info_display_row(info_content, label_text, value_text, i)

        # 警告信息
        warning_frame = tk.Frame(content_frame,
                               bg=self.colors['warning_light'],
                               relief="flat",
                               bd=1,
                               highlightthickness=1,
                               highlightcolor=self.colors['warning'],
                               highlightbackground=self.colors['warning'])
        warning_frame.pack(fill="x", pady=(0, 15))

        warning_content = tk.Frame(warning_frame, bg=self.colors['warning_light'])
        warning_content.pack(fill="x", padx=15, pady=12)

        warning_text = tk.Label(warning_content,
                              text="⚠️ 注意：退房后将从押金总额中减去相应金额",
                              font=self.fonts['body_bold'],
                              bg=self.colors['warning_light'],
                              fg=self.colors['white'],
                              wraplength=500)
        warning_text.pack()

        # 确认退房处理函数
        def confirm_checkout():
            # 退房时从押金总额中减去相应金额
            deposit_amount = room["deposit"]
            self.total_deposit_collected -= deposit_amount

            # 确保押金总额不为负数
            if self.total_deposit_collected < 0:
                self.total_deposit_collected = 0

            room["tenant_ids"] = []
            room["start_date"] = None
            room["end_date"] = None
            room["status"] = "闲置"

            self.save_data()
            self.refresh_cards()
            checkout_window.destroy()
            messagebox.showinfo("✅ 退房成功",
                              f"房间 {room['room_number']} 退房办理成功！\n\n"
                              f"退还押金: ¥{deposit_amount:.0f}\n"
                              f"剩余押金总额: ¥{self.total_deposit_collected:.0f}")

        def cancel_checkout():
            checkout_window.destroy()

        # 添加分隔线
        separator = tk.Frame(button_container, height=2, bg=self.colors['border'])
        separator.pack(fill="x", padx=20)

        # 按钮区域 - 固定在底部，确保始终可见
        button_frame = tk.Frame(button_container, bg=self.colors['background'])
        button_frame.pack(expand=True, fill="both", padx=20, pady=15)

        # 取消按钮
        cancel_btn = tk.Button(button_frame,
                             text="❌ 取消",
                             font=self.fonts['button'],
                             bg=self.colors['medium_gray'],
                             fg=self.colors['text_primary'],
                             relief="flat",
                             bd=0,
                             padx=30,
                             pady=12,
                             command=cancel_checkout,
                             cursor="hand2")
        cancel_btn.pack(side="right", padx=(15, 0))

        # 确认退房按钮 - 突出显示
        confirm_btn = tk.Button(button_frame,
                              text="🚪 确认退房",
                              font=self.fonts['button'],
                              bg=self.colors['danger'],
                              fg=self.colors['white'],
                              relief="flat",
                              bd=0,
                              padx=30,
                              pady=12,
                              command=confirm_checkout,
                              cursor="hand2")
        confirm_btn.pack(side="right")

        # 强制更新显示
        checkout_window.update_idletasks()

        # 按钮悬停效果
        def on_confirm_enter(e):
            confirm_btn.configure(bg=self.colors['danger_light'])
        def on_confirm_leave(e):
            confirm_btn.configure(bg=self.colors['danger'])
        def on_cancel_enter(e):
            cancel_btn.configure(bg=self.colors['border'])
        def on_cancel_leave(e):
            cancel_btn.configure(bg=self.colors['medium_gray'])

        confirm_btn.bind("<Enter>", on_confirm_enter)
        confirm_btn.bind("<Leave>", on_confirm_leave)
        cancel_btn.bind("<Enter>", on_cancel_enter)
        cancel_btn.bind("<Leave>", on_cancel_leave)

    def show_edit_note_dialog(self, room):
        """显示编辑备注对话框"""
        # 创建现代化的备注编辑对话框
        note_dialog = tk.Toplevel(self.root)
        note_dialog.title(f"📝 编辑房间 {room['room_number']} 备注")
        note_dialog.geometry("500x400")
        note_dialog.configure(bg=self.colors['background'])
        note_dialog.transient(self.root)
        note_dialog.grab_set()

        # 窗口居中
        note_dialog.update_idletasks()
        width = note_dialog.winfo_width()
        height = note_dialog.winfo_height()
        x = (note_dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (note_dialog.winfo_screenheight() // 2) - (height // 2)
        note_dialog.geometry(f"{width}x{height}+{x}+{y}")

        # 标题
        title_label = tk.Label(note_dialog,
                             text=f"房间 {room['room_number']} 备注编辑",
                             font=('Segoe UI', 16, 'bold'),
                             fg=self.colors['text_primary'],
                             bg=self.colors['background'])
        title_label.pack(pady=20)

        # 备注输入区域
        note_frame = tk.Frame(note_dialog, bg=self.colors['background'])
        note_frame.pack(fill="both", expand=True, padx=30, pady=(0, 20))

        tk.Label(note_frame,
                text="备注内容:",
                font=('Segoe UI', 12, 'bold'),
                fg=self.colors['text_primary'],
                bg=self.colors['background']).pack(anchor="w", pady=(0, 10))

        # 文本输入框
        note_text = tk.Text(note_frame,
                          font=('Segoe UI', 11),
                          bg=self.colors['white'],
                          fg=self.colors['text_primary'],
                          relief="solid",
                          bd=1,
                          wrap="word")
        note_text.pack(fill="both", expand=True, pady=(0, 20))

        # 加载现有备注
        current_note = room.get('note', '')
        if current_note:
            note_text.insert("1.0", current_note)

        # 按钮区域
        button_frame = tk.Frame(note_dialog, bg=self.colors['background'])
        button_frame.pack(fill="x", padx=30, pady=(0, 20))

        def save_note():
            new_note = note_text.get("1.0", "end-1c").strip()
            room['note'] = new_note
            self.save_data()
            note_dialog.destroy()
            messagebox.showinfo("✅ 保存成功", "备注已保存")

        def cancel_edit():
            note_dialog.destroy()

        # 取消按钮
        cancel_btn = tk.Button(button_frame,
                             text="❌ 取消",
                             font=('Segoe UI', 11, 'bold'),
                             bg=self.colors['danger'],
                             fg=self.colors['white'],
                             relief="flat",
                             bd=0,
                             padx=20,
                             pady=8,
                             command=cancel_edit,
                             cursor="hand2")
        cancel_btn.pack(side="right", padx=(10, 0))

        # 保存按钮
        save_btn = tk.Button(button_frame,
                           text="✅ 保存",
                           font=('Segoe UI', 11, 'bold'),
                           bg=self.colors['success'],
                           fg=self.colors['white'],
                           relief="flat",
                           bd=0,
                           padx=20,
                           pady=8,
                           command=save_note,
                           cursor="hand2")
        save_btn.pack(side="right")

    def show_room_history_dialog(self, room):
        """显示房间历史记录对话框"""
        # 创建现代化的历史记录对话框
        history_dialog = tk.Toplevel(self.root)
        history_dialog.title(f"📊 房间 {room['room_number']} 历史记录")
        history_dialog.geometry("900x700")  # 增加窗口尺寸以确保所有内容都能显示
        history_dialog.configure(bg=self.colors['background'])
        history_dialog.transient(self.root)
        history_dialog.grab_set()

        # 窗口居中
        history_dialog.update_idletasks()
        width = 900
        height = 700
        x = (history_dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (history_dialog.winfo_screenheight() // 2) - (height // 2)
        history_dialog.geometry(f"{width}x{height}+{x}+{y}")

        # 标题
        title_label = tk.Label(history_dialog,
                             text=f"房间 {room['room_number']} 历史记录",
                             font=('Segoe UI', 16, 'bold'),
                             fg=self.colors['text_primary'],
                             bg=self.colors['background'])
        title_label.pack(pady=20)

        # 内容区域
        content_frame = tk.Frame(history_dialog, bg=self.colors['white'], relief="flat", bd=0)
        content_frame.pack(fill="both", expand=True, padx=30, pady=(0, 20))

        # 滚动文本框
        text_widget = tk.Text(content_frame,
                            font=('Segoe UI', 10),
                            bg=self.colors['white'],
                            fg=self.colors['text_primary'],
                            relief="flat",
                            wrap="word",
                            state="normal")

        scrollbar = tk.Scrollbar(content_frame, orient="vertical", command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)

        # 生成历史记录内容 - 按照精确的格式要求
        history_content = "房间基本信息\n"
        history_content += "-" * 35 + "\n"
        history_content += f"房间号：{room['room_number']}\n"

        # 更新状态显示文本
        status_display = room['status']
        if room['status'] == '已出租':
            status_display = '租赁中'
        history_content += f"房间状态：{status_display}\n"

        # 获取租户信息
        tenant_info = ""
        id_card_info = ""
        phone_info = ""
        if room.get('tenant_ids'):
            tenant_names = []
            id_cards = []
            phones = []
            for tenant_id in room['tenant_ids']:
                tenant = next((p for p in self.persons if p["id"] == tenant_id), None)
                if tenant:
                    tenant_names.append(tenant.get('name', ''))
                    id_cards.append(tenant.get('id_card', ''))
                    phones.append(tenant.get('phone', ''))

            tenant_info = ', '.join(tenant_names) if tenant_names else ""
            id_card_info = ', '.join(id_cards) if id_cards else ""
            phone_info = ', '.join(phones) if phones else ""

        history_content += f"租户：{tenant_info}\n"
        history_content += f"身份证号：{id_card_info}\n"
        history_content += f"手机号：{phone_info}\n"
        history_content += f"租金：{room['rent']:.0f}\n"
        history_content += f"押金：{room['deposit']:.0f}\n"
        history_content += f"电表：{int(room.get('electricity_fee', 0))}\n"

        # 只有已出租状态才显示起租日期和截止日期
        if room['status'] == '已出租':
            history_content += f"起租日期：{room.get('start_date', '')}\n"
            history_content += f"截止日期：{room.get('end_date', '')}\n"

        history_content += "-" * 35 + "\n"
        history_content += f"备注信息：{room.get('note', '')}"

        text_widget.insert("1.0", history_content)
        text_widget.config(state="disabled")

        text_widget.pack(side="left", fill="both", expand=True, padx=20, pady=20)
        scrollbar.pack(side="right", fill="y", pady=20)

        # 按钮区域
        button_frame = tk.Frame(history_dialog, bg=self.colors['background'])
        button_frame.pack(fill="x", padx=30, pady=20)

        # 复制按钮功能
        def copy_room_info():
            try:
                # 复制内容到剪贴板
                history_dialog.clipboard_clear()
                history_dialog.clipboard_append(history_content)
                history_dialog.update()  # 确保剪贴板更新

                # 显示成功消息
                messagebox.showinfo("✅ 复制成功", "房间信息已复制到剪贴板")

                # 自动关闭对话框
                history_dialog.destroy()
            except Exception as e:
                messagebox.showerror("❌ 复制失败", f"复制失败：{str(e)}")

        # 复制按钮 - 居中显示
        copy_btn = tk.Button(button_frame,
                            text="📋 复制房间信息",
                            font=('Segoe UI', 12, 'bold'),
                            bg=self.colors['primary'],
                            fg=self.colors['white'],
                            relief="flat",
                            bd=0,
                            padx=40,
                            pady=12,
                            command=copy_room_info,
                            cursor="hand2")
        copy_btn.pack(expand=True)

    def rent_room(self, room):
        # 选择租户
        tenant = self.select_person()
        if not tenant:
            return
        
        # 选择租期
        rent_period = self.select_rent_period()
        if not rent_period:
            return
        
        # 计算起租和截至日期
        today = datetime.now().date()
        start_date = today
        end_date = today
        
        if rent_period == "周租":
            end_date = today + timedelta(weeks=1)
        elif rent_period == "月租":
            end_date = today + timedelta(days=30)
        elif rent_period == "季租":
            end_date = today + timedelta(days=90)
        elif rent_period == "半年租":
            end_date = today + timedelta(days=180)
        elif rent_period == "年租":
            end_date = today + timedelta(days=365)
        else:
            messagebox.showerror("错误", "无效的租期选择")
            return
        
        # 首次租房时累加租金和押金到总额中
        # 注意：电表读数不自动累加到总电量中，需要手动管理
        rent_amount = room["rent"]
        deposit_amount = room["deposit"]
        electricity_amount = room.get("electricity_fee", 0)
        self.total_rent_collected += rent_amount
        self.total_deposit_collected += deposit_amount
        self.total_electricity_collected += electricity_amount  # 保留电费财务记录

        # 更新房间信息
        room["tenant_ids"] = [t["id"] for t in tenant]
        room["start_date"] = start_date.strftime("%Y-%m-%d")
        room["end_date"] = end_date.strftime("%Y-%m-%d")
        room["status"] = "已出租"

        self.save_data()
        self.refresh_cards()
        messagebox.showinfo("✅ 租房成功",
                          f"租房办理成功！\n\n"
                          f"收取租金: ¥{rent_amount:.0f}\n"
                          f"收取押金: ¥{deposit_amount:.0f}\n"
                          f"收取电费: ¥{electricity_amount:.0f}\n"
                          f"累计租金总额: ¥{self.total_rent_collected:.0f}\n"
                          f"累计押金总额: ¥{self.total_deposit_collected:.0f}\n"
                          f"累计电费总额: ¥{self.total_electricity_collected:.0f}")
    
    def extend_rent(self, room):
        """现代化的办理续租对话框"""
        # 创建现代化续租窗口
        extend_window = tk.Toplevel(self.root)
        extend_window.title("🔄 办理续租")
        extend_window.geometry("600x650")
        extend_window.configure(bg=self.colors['background'])
        extend_window.resizable(False, False)
        extend_window.transient(self.root)
        extend_window.grab_set()

        # 窗口居中
        extend_window.update_idletasks()
        width = 600
        height = 650
        x = (extend_window.winfo_screenwidth() // 2) - (width // 2)
        y = (extend_window.winfo_screenheight() // 2) - (height // 2)
        extend_window.geometry(f"{width}x{height}+{x}+{y}")

        # 主容器
        main_frame = tk.Frame(extend_window, bg=self.colors['background'])
        main_frame.pack(fill="both", expand=True, padx=30, pady=20)

        # 标题区域
        title_frame = tk.Frame(main_frame, bg=self.colors['background'])
        title_frame.pack(fill="x", pady=(0, 25))

        title_label = tk.Label(title_frame,
                             text="办理续租",
                             font=self.fonts['heading'],
                             bg=self.colors['background'],
                             fg=self.colors['primary'])
        title_label.pack()

        subtitle_label = tk.Label(title_frame,
                                text=f"房间 {room['room_number']} 续租办理",
                                font=self.fonts['caption'],
                                bg=self.colors['background'],
                                fg=self.colors['text_secondary'])
        subtitle_label.pack(pady=(5, 0))

        # 当前租期信息卡片
        current_card = tk.Frame(main_frame,
                              bg=self.colors['card_bg'],
                              relief="flat",
                              bd=1,
                              highlightthickness=1,
                              highlightcolor=self.colors['border'],
                              highlightbackground=self.colors['border'])
        current_card.pack(fill="x", pady=(0, 20))

        # 卡片标题
        current_title_frame = tk.Frame(current_card, bg=self.colors['card_bg'])
        current_title_frame.pack(fill="x", padx=20, pady=(15, 10))

        current_title_icon = tk.Label(current_title_frame,
                                    text="📋",
                                    font=("Segoe UI Emoji", 14),
                                    bg=self.colors['card_bg'])
        current_title_icon.pack(side="left")

        current_title_label = tk.Label(current_title_frame,
                                     text="当前租期信息",
                                     font=self.fonts['subheading'],
                                     bg=self.colors['card_bg'],
                                     fg=self.colors['primary'])
        current_title_label.pack(side="left", padx=(8, 0))

        # 获取租户信息
        tenant_names = []
        for tenant_id in room.get("tenant_ids", []):
            tenant = next((p for p in self.persons if p["id"] == tenant_id), None)
            if tenant:
                tenant_names.append(tenant['name'])

        tenant_display = ", ".join(tenant_names) if tenant_names else "未知租户"

        # 当前信息内容
        current_content = tk.Frame(current_card, bg=self.colors['card_bg'])
        current_content.pack(fill="x", padx=20, pady=(0, 20))

        # 信息行
        info_data = [
            ("房号:", room['room_number']),
            ("租户:", tenant_display),
            ("起租日期:", room['start_date']),
            ("当前截止日期:", room['end_date'])
        ]

        for i, (label_text, value_text) in enumerate(info_data):
            self.create_info_display_row(current_content, label_text, value_text, i)

        # 续租选项卡片
        options_card = tk.Frame(main_frame,
                              bg=self.colors['card_bg'],
                              relief="flat",
                              bd=1,
                              highlightthickness=1,
                              highlightcolor=self.colors['border'],
                              highlightbackground=self.colors['border'])
        options_card.pack(fill="x", pady=(0, 20))

        # 卡片标题
        options_title_frame = tk.Frame(options_card, bg=self.colors['card_bg'])
        options_title_frame.pack(fill="x", padx=20, pady=(15, 10))

        options_title_icon = tk.Label(options_title_frame,
                                    text="⚙️",
                                    font=("Segoe UI Emoji", 14),
                                    bg=self.colors['card_bg'])
        options_title_icon.pack(side="left")

        options_title_label = tk.Label(options_title_frame,
                                     text="续租选项",
                                     font=self.fonts['subheading'],
                                     bg=self.colors['card_bg'],
                                     fg=self.colors['primary'])
        options_title_label.pack(side="left", padx=(8, 0))

        # 续租选项内容
        options_content = tk.Frame(options_card, bg=self.colors['card_bg'])
        options_content.pack(fill="x", padx=20, pady=(0, 20))

        # 租期类型选择
        tk.Label(options_content,
                text="续租类型:",
                font=self.fonts['body_bold'],
                bg=self.colors['card_bg'],
                fg=self.colors['text_secondary']).grid(row=0, column=0, padx=(0, 15), pady=(0, 15), sticky="e")

        period_var = tk.StringVar(value="月租")
        period_combobox = ttk.Combobox(options_content,
                                     textvariable=period_var,
                                     values=["周租", "月租", "季租", "半年租", "年租"],
                                     state="readonly",
                                     width=15,
                                     style="Modern.TCombobox")
        period_combobox.grid(row=0, column=1, pady=(0, 15), sticky="ew")

        # 新截止日期显示
        tk.Label(options_content,
                text="新截止日期:",
                font=self.fonts['body_bold'],
                bg=self.colors['card_bg'],
                fg=self.colors['text_secondary']).grid(row=1, column=0, padx=(0, 15), pady=(0, 15), sticky="e")

        new_end_date_var = tk.StringVar()
        new_end_date_frame = tk.Frame(options_content,
                                    bg=self.colors['white'],
                                    relief="solid",
                                    bd=1)
        new_end_date_frame.grid(row=1, column=1, pady=(0, 15), sticky="ew")

        new_end_date_label = tk.Label(new_end_date_frame,
                                    textvariable=new_end_date_var,
                                    font=self.fonts['body'],
                                    bg=self.colors['white'],
                                    fg=self.colors['text_primary'])
        new_end_date_label.pack(padx=10, pady=6)

        options_content.grid_columnconfigure(1, weight=1)

        # 计算新截止日期
        def update_new_end_date(*args):
            # 从当前截止日期开始计算
            try:
                from datetime import datetime, timedelta
                current_end_date = datetime.strptime(room['end_date'], "%Y-%m-%d").date()
                period = period_var.get()

                if period == "周租":
                    new_end_date = current_end_date + timedelta(weeks=1)
                elif period == "月租":
                    new_end_date = current_end_date + timedelta(days=30)
                elif period == "季租":
                    new_end_date = current_end_date + timedelta(days=90)
                elif period == "半年租":
                    new_end_date = current_end_date + timedelta(days=180)
                elif period == "年租":
                    new_end_date = current_end_date + timedelta(days=365)

                new_end_date_var.set(new_end_date.strftime("%Y-%m-%d"))
            except Exception as e:
                messagebox.showerror("❌ 错误", f"计算日期时出错: {e}")
                new_end_date_var.set("计算错误")

        period_var.trace_add("write", update_new_end_date)
        update_new_end_date()  # 初始化日期

        # 确认按钮
        def confirm_extend():
            try:
                # 续租时累加租金到总额中，但不修改押金
                # 电表读数不自动累加到总电量中
                rent_amount = room["rent"]
                electricity_amount = room.get("electricity_fee", 0)
                self.total_rent_collected += rent_amount
                self.total_electricity_collected += electricity_amount  # 保留电费财务记录

                # 更新房间截止日期
                new_end_date = new_end_date_var.get()
                room["end_date"] = new_end_date

                self.save_data()
                self.refresh_cards()
                extend_window.destroy()
                messagebox.showinfo("✅ 续租成功",
                                  f"续租办理成功！\n\n"
                                  f"本次续租收取租金: ¥{rent_amount:.0f}\n"
                                  f"本次续租收取电费: ¥{electricity_amount:.0f}\n"
                                  f"累计租金总额: ¥{self.total_rent_collected:.0f}\n"
                                  f"累计电费总额: ¥{self.total_electricity_collected:.0f}")
            except Exception as e:
                messagebox.showerror("❌ 错误", f"续租办理失败: {e}")

        def cancel_extend():
            extend_window.destroy()

        # 按钮区域
        button_frame = tk.Frame(main_frame, bg=self.colors['background'])
        button_frame.pack(fill="x")

        # 取消按钮
        cancel_btn = tk.Button(button_frame,
                             text="❌ 取消",
                             font=self.fonts['button'],
                             bg=self.colors['danger'],
                             fg=self.colors['white'],
                             relief="flat",
                             bd=0,
                             padx=25,
                             pady=10,
                             command=cancel_extend,
                             cursor="hand2")
        cancel_btn.pack(side="right", padx=(10, 0))

        # 确认续租按钮
        confirm_btn = tk.Button(button_frame,
                              text="✅ 确认续租",
                              font=self.fonts['button'],
                              bg=self.colors['secondary'],
                              fg=self.colors['white'],
                              relief="flat",
                              bd=0,
                              padx=25,
                              pady=10,
                              command=confirm_extend,
                              cursor="hand2")
        confirm_btn.pack(side="right")

        # 按钮悬停效果
        def on_confirm_enter(e):
            confirm_btn.configure(bg=self.colors['secondary_light'])
        def on_confirm_leave(e):
            confirm_btn.configure(bg=self.colors['secondary'])
        def on_cancel_enter(e):
            cancel_btn.configure(bg=self.colors['danger_light'])
        def on_cancel_leave(e):
            cancel_btn.configure(bg=self.colors['danger'])

        confirm_btn.bind("<Enter>", on_confirm_enter)
        confirm_btn.bind("<Leave>", on_confirm_leave)
        cancel_btn.bind("<Enter>", on_cancel_enter)
        cancel_btn.bind("<Leave>", on_cancel_leave)

    def create_info_display_row(self, parent, label_text, value_text, row):
        """创建信息显示行"""
        # 标签
        label = tk.Label(parent,
                        text=label_text,
                        font=self.fonts['body_bold'],
                        bg=self.colors['card_bg'],
                        fg=self.colors['text_secondary'])
        label.grid(row=row, column=0, padx=(0, 15), pady=6, sticky="w")

        # 值
        value_label = tk.Label(parent,
                             text=value_text,
                             font=self.fonts['body'],
                             bg=self.colors['card_bg'],
                             fg=self.colors['text_primary'])
        value_label.grid(row=row, column=1, pady=6, sticky="w")


    
    def check_out(self, room):
        if messagebox.askyesno("确认", "确定要办理退房吗？"):
            room["tenant_ids"] = []
            room["start_date"] = None
            room["end_date"] = None
            room["status"] = "闲置"
            
            self.save_data()
            self.refresh_cards()
            messagebox.showinfo("成功", "退房办理成功")
    
    def edit_room(self, room):
        edit_window = tk.Toplevel(self.root)
        edit_window.title("编辑房屋信息")
        # 窗口居中
        edit_window.update_idletasks()
        width = edit_window.winfo_width()
        height = edit_window.winfo_height()
        x = (edit_window.winfo_screenwidth() // 2) - (width // 2)
        y = (edit_window.winfo_screenheight() // 2) - (height // 2)
        edit_window.geometry('{}x{}+{}+{}'.format(width, height, x, y))
        
        ttk.Label(edit_window, text="房号:").grid(row=0, column=0, padx=5, pady=5)
        room_number_entry = ttk.Entry(edit_window)
        room_number_entry.insert(0, room["room_number"])
        room_number_entry.grid(row=0, column=1, padx=5, pady=5)
        
        ttk.Label(edit_window, text="租金:").grid(row=1, column=0, padx=5, pady=5)
        rent_entry = ttk.Entry(edit_window)
        rent_entry.insert(0, room["rent"])
        rent_entry.grid(row=1, column=1, padx=5, pady=5)
        
        ttk.Label(edit_window, text="押金:").grid(row=2, column=0, padx=5, pady=5)
        deposit_entry = ttk.Entry(edit_window)
        deposit_entry.insert(0, room["deposit"])
        deposit_entry.grid(row=2, column=1, padx=5, pady=5)
        
        def save_changes():
            room["room_number"] = room_number_entry.get()
            room["rent"] = float(rent_entry.get())
            room["deposit"] = float(deposit_entry.get())
            
            self.save_data()
            self.refresh_cards()
            edit_window.destroy()
            messagebox.showinfo("成功", "房屋信息已更新")
        
        ttk.Button(edit_window, text="保存", command=save_changes).grid(row=3, column=1, pady=10)
    
    def delete_room(self, room):
        if messagebox.askyesno("确认", f"确定要删除房号为 {room['room_number']} 的房屋吗？"):
            self.rooms.remove(room)
            self.save_data()
            self.refresh_cards()
            messagebox.showinfo("成功", "房屋已删除")
    
    def add_room(self):
        # 创建现代化的添加房间对话框
        add_window = tk.Toplevel(self.root)
        add_window.title("🏠 添加新房间")
        add_window.configure(bg=self.colors['background'])
        add_window.resizable(False, False)

        # 设置窗口大小和居中
        window_width, window_height = 400, 400
        add_window.geometry(f"{window_width}x{window_height}")

        # 窗口居中
        add_window.update_idletasks()
        x = (add_window.winfo_screenwidth() // 2) - (window_width // 2)
        y = (add_window.winfo_screenheight() // 2) - (window_height // 2)
        add_window.geometry(f'{window_width}x{window_height}+{x}+{y}')

        # 设置窗口属性
        add_window.transient(self.root)
        add_window.grab_set()

        # 创建主容器
        main_container = tk.Frame(add_window, bg=self.colors['background'])
        main_container.pack(fill="both", expand=True, padx=30, pady=30)

        # 标题区域
        title_frame = tk.Frame(main_container, bg=self.colors['background'])
        title_frame.pack(fill="x", pady=(0, 25))

        title_label = tk.Label(title_frame,
                             text="🏠 添加新房间",
                             font=self.fonts['heading'],
                             bg=self.colors['background'],
                             fg=self.colors['primary'])
        title_label.pack()

        subtitle_label = tk.Label(title_frame,
                                text="请填写房间基本信息",
                                font=self.fonts['caption'],
                                bg=self.colors['background'],
                                fg=self.colors['text_secondary'])
        subtitle_label.pack(pady=(5, 0))

        # 表单区域
        form_frame = tk.Frame(main_container, bg=self.colors['background'])
        form_frame.pack(fill="x", pady=(0, 25))

        # 房号输入
        tk.Label(form_frame, text="房间号:",
                bg=self.colors['background'],
                fg=self.colors['text_primary'],
                font=self.fonts['body_bold']).grid(row=0, column=0, padx=(0, 15), pady=12, sticky="e")
        room_number_entry = tk.Entry(form_frame,
                                   font=self.fonts['body'],
                                   bg=self.colors['white'],
                                   fg=self.colors['text_primary'],
                                   relief="solid",
                                   bd=1,
                                   highlightthickness=1,
                                   highlightcolor=self.colors['primary'])
        room_number_entry.grid(row=0, column=1, padx=(0, 0), pady=12, sticky="ew")

        # 租金输入
        tk.Label(form_frame, text="月租金:",
                bg=self.colors['background'],
                fg=self.colors['text_primary'],
                font=self.fonts['body_bold']).grid(row=1, column=0, padx=(0, 15), pady=12, sticky="e")
        rent_entry = tk.Entry(form_frame,
                            font=self.fonts['body'],
                            bg=self.colors['white'],
                            fg=self.colors['text_primary'],
                            relief="solid",
                            bd=1,
                            highlightthickness=1,
                            highlightcolor=self.colors['primary'])
        rent_entry.grid(row=1, column=1, padx=(0, 0), pady=12, sticky="ew")

        # 押金输入
        tk.Label(form_frame, text="押金:",
                bg=self.colors['background'],
                fg=self.colors['text_primary'],
                font=self.fonts['body_bold']).grid(row=2, column=0, padx=(0, 15), pady=12, sticky="e")
        deposit_entry = tk.Entry(form_frame,
                               font=self.fonts['body'],
                               bg=self.colors['white'],
                               fg=self.colors['text_primary'],
                               relief="solid",
                               bd=1,
                               highlightthickness=1,
                               highlightcolor=self.colors['primary'])
        deposit_entry.grid(row=2, column=1, padx=(0, 0), pady=12, sticky="ew")

        # 电表输入
        tk.Label(form_frame, text="⚡ 电表读数:",
                bg=self.colors['background'],
                fg=self.colors['text_primary'],
                font=self.fonts['body_bold']).grid(row=3, column=0, padx=(0, 15), pady=12, sticky="e")
        electricity_entry = tk.Entry(form_frame,
                                    font=self.fonts['body'],
                                    bg=self.colors['white'],
                                    fg=self.colors['text_primary'],
                                    relief="solid",
                                    bd=1,
                                    highlightthickness=1,
                                    highlightcolor=self.colors['primary'])
        electricity_entry.insert(0, "0")  # 默认值为0
        electricity_entry.grid(row=3, column=1, padx=(0, 0), pady=12, sticky="ew")

        # 配置列权重
        form_frame.grid_columnconfigure(1, weight=1)
        
        def save_room():
            try:
                # 电表读数只接受整数
                electricity_value = electricity_entry.get().strip()
                electricity_int = int(float(electricity_value)) if electricity_value else 0

                new_room = {
                    "id": len(self.rooms) + 1,
                    "room_number": room_number_entry.get(),
                    "rent": float(rent_entry.get()),
                    "deposit": float(deposit_entry.get()),
                    "electricity_fee": electricity_int,
                    "tenant_id": None,
                    "start_date": None,
                    "end_date": None,
                    "status": "闲置",
                    "note": ""
                }

                self.rooms.append(new_room)
                self.save_data()
                self.refresh_cards()
                add_window.destroy()

                # 显示成功消息并询问是否立即进行人员注册
                if messagebox.askyesno("✅ 添加成功",
                                     f"房间 {new_room['room_number']} 已成功添加！\n\n"
                                     f"是否立即打开房间管理窗口进行人员注册？"):
                    # 添加新房间后直接导航到房间管理窗口
                    self.show_multi_function_window(new_room)
            except ValueError:
                messagebox.showerror("❌ 错误", "请输入有效的数字（电表读数必须为整数）")

        # 按钮区域
        button_frame = tk.Frame(main_container, bg=self.colors['background'])
        button_frame.pack(fill="x")

        # 现代化按钮样式
        def create_modern_button(parent, text, command, bg_color, hover_color):
            btn = tk.Button(parent,
                          text=text,
                          font=self.fonts['button'],
                          bg=bg_color,
                          fg=self.colors['white'],
                          relief="flat",
                          bd=0,
                          padx=25,
                          pady=12,
                          command=command,
                          cursor="hand2",
                          activebackground=hover_color,
                          activeforeground=self.colors['white'])

            # 添加悬停效果
            def on_enter(e):
                btn.configure(bg=hover_color)
            def on_leave(e):
                btn.configure(bg=bg_color)

            btn.bind("<Enter>", on_enter)
            btn.bind("<Leave>", on_leave)

            return btn

        # 取消按钮
        cancel_btn = create_modern_button(button_frame,
                                        "❌ 取消",
                                        add_window.destroy,
                                        self.colors['danger'],
                                        self.colors['danger_light'])
        cancel_btn.pack(side="right", padx=(15, 0))

        # 保存按钮
        save_btn = create_modern_button(button_frame,
                                      "✅ 保存房间",
                                      save_room,
                                      self.colors['primary'],
                                      self.colors['primary_light'])
        save_btn.pack(side="right")
    
    def show_all_rooms(self):
        rooms_window = tk.Toplevel(self.root)
        rooms_window.title("所有房间信息")
        # 设置初始窗口大小
        rooms_window.geometry("800x600")
        # 窗口居中
        rooms_window.update_idletasks()
        width = rooms_window.winfo_width()
        height = rooms_window.winfo_height()
        x = (rooms_window.winfo_screenwidth() // 2) - (width // 2)
        y = (rooms_window.winfo_screenheight() // 2) - (height // 2)
        rooms_window.geometry('{}x{}+{}+{}'.format(width, height, x, y))
        
        tree = ttk.Treeview(rooms_window, columns=('id', 'room_number', 'rent', 'deposit', 'status', 'tenant', 'start_date', 'end_date'), show='headings')
        tree.heading('id', text='ID')
        tree.heading('room_number', text='房号')
        tree.heading('rent', text='租金')
        tree.heading('deposit', text='押金')
        tree.heading('status', text='状态')
        tree.heading('tenant', text='租户')
        tree.heading('start_date', text='起租日期')
        tree.heading('end_date', text='截至日期')
        
        # 设置列宽
        tree.column('id', width=50)
        tree.column('room_number', width=80)
        tree.column('rent', width=80)
        tree.column('deposit', width=80)
        tree.column('status', width=80)
        tree.column('tenant', width=100)
        tree.column('start_date', width=100)
        tree.column('end_date', width=100)
        tree.heading('tenant', text='租户')
        tree.heading('start_date', text='起租日期')
        tree.heading('end_date', text='截至日期')
        
        for room in self.rooms:
            tenant_names = '未出租' if room['status'] == '闲置' else ', '.join([p['name'] for p in self.persons if p['id'] in room.get('tenant_ids', [])]) or '未知租户'
            tree.insert('', 'end', values=(
                room['id'],
                room['room_number'],
                f'¥{room["rent"]}',
                f'¥{room["deposit"]}',
                room['status'],
                tenant_names or '',
                room['start_date'] or '',
                room['end_date'] or ''
            ))
        
        tree.pack(fill="both", expand=True, padx=10, pady=10)
    
    def select_person(self):
        """现代化的选择租户对话框"""
        if not self.persons:
            messagebox.showwarning("⚠️ 警告", "没有可选的租户信息，请先添加人员")
            return None

        select_window = tk.Toplevel(self.root)
        select_window.title("👥 选择租户")
        select_window.geometry("700x550")
        select_window.configure(bg=self.colors['background'])
        select_window.resizable(True, True)
        select_window.minsize(650, 500)
        select_window.transient(self.root)
        select_window.grab_set()

        # 窗口居中
        select_window.update_idletasks()
        width = 700
        height = 550
        x = (select_window.winfo_screenwidth() // 2) - (width // 2)
        y = (select_window.winfo_screenheight() // 2) - (height // 2)
        select_window.geometry(f"{width}x{height}+{x}+{y}")

        # 主容器
        main_frame = tk.Frame(select_window, bg=self.colors['background'])
        main_frame.pack(fill="both", expand=True, padx=30, pady=20)

        # 标题区域
        title_frame = tk.Frame(main_frame, bg=self.colors['background'])
        title_frame.pack(fill="x", pady=(0, 20))

        title_label = tk.Label(title_frame,
                             text="选择租户",
                             font=self.fonts['heading'],
                             bg=self.colors['background'],
                             fg=self.colors['primary'])
        title_label.pack()

        subtitle_label = tk.Label(title_frame,
                                text="请选择一个或多个租户",
                                font=self.fonts['caption'],
                                bg=self.colors['background'],
                                fg=self.colors['text_secondary'])
        subtitle_label.pack(pady=(5, 0))

        # 表格容器
        table_frame = tk.Frame(main_frame,
                             bg=self.colors['card_bg'],
                             relief="flat",
                             bd=1,
                             highlightthickness=1,
                             highlightcolor=self.colors['border'],
                             highlightbackground=self.colors['border'])
        table_frame.pack(fill="both", expand=True, pady=(0, 20))

        # 创建现代化带滚动条的框架
        canvas = tk.Canvas(table_frame,
                         bg=self.colors['card_bg'],
                         highlightthickness=0,
                         relief="flat",
                         bd=0)

        scrollbar = ttk.Scrollbar(table_frame,
                                orient="vertical",
                                command=canvas.yview,
                                style="InfoTab.Vertical.TScrollbar")

        scrollable_frame = tk.Frame(canvas, bg=self.colors['card_bg'])

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # 表头
        header_frame = tk.Frame(scrollable_frame, bg=self.colors['primary'])
        header_frame.pack(fill="x", padx=10, pady=(10, 0))

        headers = ["选择", "姓名", "身份证号", "联系电话"]
        header_widths = [80, 120, 180, 150]

        for i, (header, width) in enumerate(zip(headers, header_widths)):
            header_label = tk.Label(header_frame,
                                  text=header,
                                  font=self.fonts['body_bold'],
                                  bg=self.colors['primary'],
                                  fg=self.colors['white'],
                                  width=width//8)
            header_label.grid(row=0, column=i, padx=2, pady=8, sticky="ew")

        # 配置表头列权重
        for i in range(len(headers)):
            header_frame.grid_columnconfigure(i, weight=1)

        # 存储选中状态
        selected_vars = []

        # 人员数据行
        for i, person in enumerate(self.persons):
            var = tk.BooleanVar()
            selected_vars.append((person, var))

            # 行背景色交替
            row_bg = self.colors['white'] if i % 2 == 0 else self.colors['light_gray']

            row_frame = tk.Frame(scrollable_frame, bg=row_bg)
            row_frame.pack(fill="x", padx=10, pady=1)

            # 复选框
            checkbox = tk.Checkbutton(row_frame,
                                    variable=var,
                                    bg=row_bg,
                                    activebackground=row_bg,
                                    relief="flat",
                                    bd=0)
            checkbox.grid(row=0, column=0, padx=10, pady=8)

            # 姓名
            name_label = tk.Label(row_frame,
                                text=person['name'],
                                font=self.fonts['body'],
                                bg=row_bg,
                                fg=self.colors['text_primary'])
            name_label.grid(row=0, column=1, padx=10, pady=8, sticky="w")

            # 身份证号
            id_card_label = tk.Label(row_frame,
                                   text=person.get('id_card', ''),
                                   font=self.fonts['body'],
                                   bg=row_bg,
                                   fg=self.colors['text_primary'])
            id_card_label.grid(row=0, column=2, padx=10, pady=8, sticky="w")

            # 电话
            phone_label = tk.Label(row_frame,
                                 text=person.get('phone', ''),
                                 font=self.fonts['body'],
                                 bg=row_bg,
                                 fg=self.colors['text_primary'])
            phone_label.grid(row=0, column=3, padx=10, pady=8, sticky="w")

            # 配置行列权重
            for j in range(4):
                row_frame.grid_columnconfigure(j, weight=1)

            # 双击选择功能
            def on_double_click(event, p=person):
                # 选中当前租户
                for person_item, var_item in selected_vars:
                    if person_item == p:
                        var_item.set(True)
                    else:
                        var_item.set(False)
                # 立即确认选择
                on_select()

            # 为整行绑定双击事件
            for widget in [row_frame, name_label, id_card_label, phone_label]:
                widget.bind("<Double-Button-1>", on_double_click)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        selected_persons = []

        def on_select():
            nonlocal selected_persons
            selected_persons = [p for p, var in selected_vars if var.get()]
            if not selected_persons:
                messagebox.showwarning("⚠️ 警告", "请至少选择一个租户")
                return
            select_window.destroy()

        def on_cancel():
            nonlocal selected_persons
            selected_persons = []
            select_window.destroy()

        # 按钮区域
        button_frame = tk.Frame(main_frame, bg=self.colors['background'])
        button_frame.pack(fill="x")

        # 取消按钮
        cancel_btn = tk.Button(button_frame,
                             text="❌ 取消",
                             font=self.fonts['button'],
                             bg=self.colors['danger'],
                             fg=self.colors['white'],
                             relief="flat",
                             bd=0,
                             padx=25,
                             pady=10,
                             command=on_cancel,
                             cursor="hand2")
        cancel_btn.pack(side="right", padx=(10, 0))

        # 确认选择按钮
        confirm_btn = tk.Button(button_frame,
                              text="✅ 确认选择",
                              font=self.fonts['button'],
                              bg=self.colors['secondary'],
                              fg=self.colors['white'],
                              relief="flat",
                              bd=0,
                              padx=25,
                              pady=10,
                              command=on_select,
                              cursor="hand2")
        confirm_btn.pack(side="right")

        # 按钮悬停效果
        def on_confirm_enter(e):
            confirm_btn.configure(bg=self.colors['secondary_light'])
        def on_confirm_leave(e):
            confirm_btn.configure(bg=self.colors['secondary'])
        def on_cancel_enter(e):
            cancel_btn.configure(bg=self.colors['danger_light'])
        def on_cancel_leave(e):
            cancel_btn.configure(bg=self.colors['danger'])

        confirm_btn.bind("<Enter>", on_confirm_enter)
        confirm_btn.bind("<Leave>", on_confirm_leave)
        cancel_btn.bind("<Enter>", on_cancel_enter)
        cancel_btn.bind("<Leave>", on_cancel_leave)

        select_window.wait_window()
        return selected_persons if selected_persons else None

    def show_tenant_selection_window(self, room):
        """显示现代化的租户选择窗口，用于导航流程"""
        if not self.persons:
            messagebox.showwarning("⚠️ 警告", "没有可选的租户信息，请先添加人员")
            return

        select_window = tk.Toplevel(self.root)
        select_window.title("👥 选择租户")
        select_window.geometry("700x550")
        select_window.configure(bg=self.colors['background'])
        select_window.resizable(True, True)
        select_window.minsize(650, 500)
        select_window.transient(self.root)
        select_window.grab_set()

        # 窗口居中
        select_window.update_idletasks()
        width = 700
        height = 550
        x = (select_window.winfo_screenwidth() // 2) - (width // 2)
        y = (select_window.winfo_screenheight() // 2) - (height // 2)
        select_window.geometry(f"{width}x{height}+{x}+{y}")

        # 主容器
        main_frame = tk.Frame(select_window, bg=self.colors['background'])
        main_frame.pack(fill="both", expand=True, padx=30, pady=20)

        # 标题区域
        title_frame = tk.Frame(main_frame, bg=self.colors['background'])
        title_frame.pack(fill="x", pady=(0, 20))

        title_label = tk.Label(title_frame,
                             text="选择租户",
                             font=self.fonts['heading'],
                             bg=self.colors['background'],
                             fg=self.colors['primary'])
        title_label.pack()

        subtitle_label = tk.Label(title_frame,
                                text=f"为房间 {room['room_number']} 选择租户",
                                font=self.fonts['caption'],
                                bg=self.colors['background'],
                                fg=self.colors['text_secondary'])
        subtitle_label.pack(pady=(5, 0))

        # 表格容器
        table_container = tk.Frame(main_frame,
                                 bg=self.colors['card_bg'],
                                 relief="flat",
                                 bd=1,
                                 highlightthickness=1,
                                 highlightcolor=self.colors['border'],
                                 highlightbackground=self.colors['border'])
        table_container.pack(fill="both", expand=True, pady=(0, 20))

        # 创建现代化滚动条的Canvas
        canvas = tk.Canvas(table_container,
                         bg=self.colors['card_bg'],
                         highlightthickness=0,
                         relief="flat",
                         bd=0)

        # 现代化滚动条
        scrollbar = ttk.Scrollbar(table_container,
                                orient="vertical",
                                command=canvas.yview,
                                style="MainView.Vertical.TScrollbar")

        scrollable_frame = tk.Frame(canvas, bg=self.colors['card_bg'])

        def configure_scroll_region(event=None):
            canvas.configure(scrollregion=canvas.bbox("all"))

        scrollable_frame.bind("<Configure>", configure_scroll_region)

        canvas_window = canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # 确保scrollable_frame的宽度与canvas匹配
        def configure_canvas_width(event):
            canvas_width = event.width
            canvas.itemconfig(canvas_window, width=canvas_width)

        canvas.bind('<Configure>', configure_canvas_width)

        # 现代化表头
        header_frame = tk.Frame(scrollable_frame,
                              bg=self.colors['primary'],
                              height=40)
        header_frame.pack(fill="x", padx=10, pady=(10, 0))
        header_frame.pack_propagate(False)

        headers = ["选择", "姓名", "身份证号", "电话"]
        header_widths = [80, 120, 180, 120]

        for col, (header, width) in enumerate(zip(headers, header_widths)):
            header_label = tk.Label(header_frame,
                                  text=header,
                                  font=self.fonts['body_bold'],
                                  bg=self.colors['primary'],
                                  fg=self.colors['white'],
                                  width=width//8)  # 近似字符宽度
            header_label.pack(side="left", padx=5, pady=8, fill="y")

        # 存储选中状态
        selected_vars = []

        # 数据行容器
        data_container = tk.Frame(scrollable_frame, bg=self.colors['card_bg'])
        data_container.pack(fill="both", expand=True, padx=10, pady=(0, 10))

        for i, person in enumerate(self.persons):
            var = tk.BooleanVar()
            selected_vars.append((person, var))

            # 现代化行容器
            row_frame = tk.Frame(data_container,
                               bg=self.colors['white'] if i % 2 == 0 else self.colors['light_gray'],
                               height=45)
            row_frame.pack(fill="x", pady=1)
            row_frame.pack_propagate(False)

            # 复选框区域
            checkbox_frame = tk.Frame(row_frame,
                                    bg=row_frame['bg'],
                                    width=80)
            checkbox_frame.pack(side="left", fill="y")
            checkbox_frame.pack_propagate(False)

            checkbox = tk.Checkbutton(checkbox_frame,
                                    variable=var,
                                    bg=row_frame['bg'],
                                    activebackground=row_frame['bg'],
                                    relief="flat",
                                    bd=0)
            checkbox.pack(expand=True)

            # 姓名区域
            name_frame = tk.Frame(row_frame,
                                bg=row_frame['bg'],
                                width=120)
            name_frame.pack(side="left", fill="y")
            name_frame.pack_propagate(False)

            name_label = tk.Label(name_frame,
                                text=person['name'],
                                font=self.fonts['body_bold'],
                                bg=row_frame['bg'],
                                fg=self.colors['text_primary'],
                                anchor="w")
            name_label.pack(expand=True, fill="both", padx=10)

            # 身份证号区域
            id_frame = tk.Frame(row_frame,
                              bg=row_frame['bg'],
                              width=180)
            id_frame.pack(side="left", fill="y")
            id_frame.pack_propagate(False)

            id_label = tk.Label(id_frame,
                              text=person.get('id_card', ''),
                              font=self.fonts['body'],
                              bg=row_frame['bg'],
                              fg=self.colors['text_secondary'],
                              anchor="w")
            id_label.pack(expand=True, fill="both", padx=10)

            # 电话区域
            phone_frame = tk.Frame(row_frame,
                                 bg=row_frame['bg'],
                                 width=120)
            phone_frame.pack(side="left", fill="y")
            phone_frame.pack_propagate(False)

            phone_label = tk.Label(phone_frame,
                                 text=person.get('phone', ''),
                                 font=self.fonts['body'],
                                 bg=row_frame['bg'],
                                 fg=self.colors['text_secondary'],
                                 anchor="w")
            phone_label.pack(expand=True, fill="both", padx=10)

            # 双击事件处理
            def on_double_click(event, p=person):
                # 选中当前租户
                for person_item, var_item in selected_vars:
                    if person_item == p:
                        var_item.set(True)
                    else:
                        var_item.set(False)
                # 导航到租期选择
                selected_persons = [p]
                select_window.destroy()
                self.show_rent_period_selection_window(room, selected_persons)

            # 悬停效果
            def on_enter(event, frame=row_frame):
                frame.configure(bg=self.colors['card_hover'])
                for child in frame.winfo_children():
                    child.configure(bg=self.colors['card_hover'])
                    for grandchild in child.winfo_children():
                        try:
                            grandchild.configure(bg=self.colors['card_hover'])
                        except tk.TclError:
                            pass

            def on_leave(event, frame=row_frame, original_bg=row_frame['bg']):
                frame.configure(bg=original_bg)
                for child in frame.winfo_children():
                    child.configure(bg=original_bg)
                    for grandchild in child.winfo_children():
                        try:
                            grandchild.configure(bg=original_bg)
                        except tk.TclError:
                            pass

            # 为行中的所有组件绑定事件
            widgets_to_bind = [row_frame, checkbox_frame, checkbox, name_frame, name_label,
                             id_frame, id_label, phone_frame, phone_label]

            for widget in widgets_to_bind:
                widget.bind("<Double-Button-1>", on_double_click)
                widget.bind("<Enter>", on_enter)
                widget.bind("<Leave>", on_leave)

        # 布局Canvas和滚动条
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # 按钮区域
        button_frame = tk.Frame(main_frame, bg=self.colors['background'])
        button_frame.pack(fill="x")

        def on_confirm_select():
            selected_persons = [p for p, var in selected_vars if var.get()]
            if not selected_persons:
                messagebox.showwarning("⚠️ 警告", "请至少选择一个租户")
                return
            select_window.destroy()
            # 导航到租期选择窗口
            self.show_rent_period_selection_window(room, selected_persons)

        def on_cancel():
            select_window.destroy()

        # 取消按钮
        cancel_btn = tk.Button(button_frame,
                             text="❌ 取消",
                             font=self.fonts['button'],
                             bg=self.colors['medium_gray'],
                             fg=self.colors['text_primary'],
                             relief="flat",
                             bd=0,
                             padx=25,
                             pady=10,
                             command=on_cancel,
                             cursor="hand2")
        cancel_btn.pack(side="right", padx=(10, 0))

        # 确认选择按钮
        confirm_btn = tk.Button(button_frame,
                              text="✅ 确认选择",
                              font=self.fonts['button'],
                              bg=self.colors['secondary'],
                              fg=self.colors['white'],
                              relief="flat",
                              bd=0,
                              padx=25,
                              pady=10,
                              command=on_confirm_select,
                              cursor="hand2")
        confirm_btn.pack(side="right")

        # 按钮悬停效果
        def on_confirm_enter(e):
            confirm_btn.configure(bg=self.colors['secondary_light'])
        def on_confirm_leave(e):
            confirm_btn.configure(bg=self.colors['secondary'])
        def on_cancel_enter(e):
            cancel_btn.configure(bg=self.colors['border'])
        def on_cancel_leave(e):
            cancel_btn.configure(bg=self.colors['medium_gray'])

        confirm_btn.bind("<Enter>", on_confirm_enter)
        confirm_btn.bind("<Leave>", on_confirm_leave)
        cancel_btn.bind("<Enter>", on_cancel_enter)
        cancel_btn.bind("<Leave>", on_cancel_leave)

    def select_rent_period(self):
        period_window = tk.Toplevel(self.root)
        period_window.title("选择租期")
        period_window.geometry("400x300")
        
        # 租期类型选择
        ttk.Label(period_window, text="租期类型:").grid(row=0, column=0, padx=10, pady=10, sticky="w")
        period_var = tk.StringVar(value="月租")
        period_combobox = ttk.Combobox(period_window, textvariable=period_var, values=["周租", "月租", "季租", "半年租", "年租"], state="readonly")
        period_combobox.grid(row=0, column=1, padx=10, pady=10, sticky="ew")
        
        # 日期显示
        ttk.Label(period_window, text="起租日期:").grid(row=1, column=0, padx=10, pady=10, sticky="w")
        start_date_entry = ttk.Entry(period_window, state="readonly")
        start_date_entry.grid(row=1, column=1, padx=10, pady=10, sticky="ew")
        
        ttk.Label(period_window, text="截至日期:").grid(row=2, column=0, padx=10, pady=10, sticky="w")
        end_date_entry = ttk.Entry(period_window, state="readonly")
        end_date_entry.grid(row=2, column=1, padx=10, pady=10, sticky="ew")
        
        # 计算日期
        def update_dates(*args):
            today = datetime.now().date()
            start_date = today
            period = period_var.get()
            
            if period == "周租":
                end_date = today + timedelta(weeks=1)
            elif period == "月租":
                end_date = today + timedelta(days=30)
            elif period == "季租":
                end_date = today + timedelta(days=90)
            elif period == "半年租":
                end_date = today + timedelta(days=180)
            elif period == "年租":
                end_date = today + timedelta(days=365)
            
            start_date_entry.config(state="normal")
            start_date_entry.delete(0, tk.END)
            start_date_entry.insert(0, start_date.strftime("%Y-%m-%d"))
            start_date_entry.config(state="readonly")
            
            end_date_entry.config(state="normal")
            end_date_entry.delete(0, tk.END)
            end_date_entry.insert(0, end_date.strftime("%Y-%m-%d"))
            end_date_entry.config(state="readonly")
        
        period_var.trace_add("write", update_dates)
        update_dates()  # 初始化日期
        
        selected_period = None
        
        def on_confirm():
            nonlocal selected_period
            selected_period = period_var.get()
            period_window.destroy()
        
        ttk.Button(period_window, text="确认", command=on_confirm).grid(row=3, column=0, columnspan=2, pady=20)
        period_window.grid_columnconfigure(1, weight=1)
        
        period_window.wait_window()
        return selected_period

    def show_rent_period_selection_window(self, room, selected_persons):
        """显示现代化的租期选择窗口，用于导航流程"""
        period_window = tk.Toplevel(self.root)
        period_window.title("📅 选择租期")
        period_window.geometry("500x450")
        period_window.configure(bg=self.colors['background'])
        period_window.resizable(False, False)
        period_window.transient(self.root)
        period_window.grab_set()

        # 窗口居中
        period_window.update_idletasks()
        width = 500
        height = 450
        x = (period_window.winfo_screenwidth() // 2) - (width // 2)
        y = (period_window.winfo_screenheight() // 2) - (height // 2)
        period_window.geometry(f"{width}x{height}+{x}+{y}")

        # 主容器
        main_frame = tk.Frame(period_window, bg=self.colors['background'])
        main_frame.pack(fill="both", expand=True, padx=30, pady=20)

        # 标题区域
        title_frame = tk.Frame(main_frame, bg=self.colors['background'])
        title_frame.pack(fill="x", pady=(0, 25))

        # 主标题
        title_label = tk.Label(title_frame,
                             text="选择租期",
                             font=self.fonts['heading'],
                             bg=self.colors['background'],
                             fg=self.colors['primary'])
        title_label.pack()

        subtitle_label = tk.Label(title_frame,
                                text=f"房间 {room['room_number']} 租期设置",
                                font=self.fonts['caption'],
                                bg=self.colors['background'],
                                fg=self.colors['text_secondary'])
        subtitle_label.pack(pady=(5, 0))

        # 租户信息卡片
        tenant_card = tk.Frame(main_frame,
                             bg=self.colors['card_bg'],
                             relief="flat",
                             bd=1,
                             highlightthickness=1,
                             highlightcolor=self.colors['border'],
                             highlightbackground=self.colors['border'])
        tenant_card.pack(fill="x", pady=(0, 20))

        tenant_content = tk.Frame(tenant_card, bg=self.colors['card_bg'])
        tenant_content.pack(fill="x", padx=20, pady=15)

        # 租户标题
        tenant_title = tk.Label(tenant_content,
                              text="👥 选中的租户",
                              font=self.fonts['body_bold'],
                              bg=self.colors['card_bg'],
                              fg=self.colors['text_primary'])
        tenant_title.pack(anchor="w")

        # 租户名称
        tenant_names = [person['name'] for person in selected_persons]
        tenant_names_label = tk.Label(tenant_content,
                                    text=", ".join(tenant_names),
                                    font=self.fonts['body'],
                                    bg=self.colors['card_bg'],
                                    fg=self.colors['secondary'])
        tenant_names_label.pack(anchor="w", pady=(5, 0))

        # 租期选择卡片
        period_card = tk.Frame(main_frame,
                             bg=self.colors['card_bg'],
                             relief="flat",
                             bd=1,
                             highlightthickness=1,
                             highlightcolor=self.colors['border'],
                             highlightbackground=self.colors['border'])
        period_card.pack(fill="x", pady=(0, 20))

        period_content = tk.Frame(period_card, bg=self.colors['card_bg'])
        period_content.pack(fill="x", padx=20, pady=20)

        # 租期类型选择
        period_type_frame = tk.Frame(period_content, bg=self.colors['card_bg'])
        period_type_frame.pack(fill="x", pady=(0, 15))

        period_type_label = tk.Label(period_type_frame,
                                   text="📋 租期类型:",
                                   font=self.fonts['body_bold'],
                                   bg=self.colors['card_bg'],
                                   fg=self.colors['text_primary'])
        period_type_label.pack(side="left")

        period_var = tk.StringVar(value="月租")
        period_combobox = ttk.Combobox(period_type_frame,
                                     textvariable=period_var,
                                     values=["周租", "月租", "季租", "半年租", "年租"],
                                     state="readonly",
                                     style="Modern.TCombobox",
                                     font=self.fonts['body'],
                                     width=15)
        period_combobox.pack(side="right")

        # 日期显示区域
        date_frame = tk.Frame(period_content, bg=self.colors['card_bg'])
        date_frame.pack(fill="x")

        # 起租日期
        start_date_frame = tk.Frame(date_frame, bg=self.colors['card_bg'])
        start_date_frame.pack(fill="x", pady=(0, 10))

        start_date_label = tk.Label(start_date_frame,
                                  text="📅 起租日期:",
                                  font=self.fonts['body_bold'],
                                  bg=self.colors['card_bg'],
                                  fg=self.colors['text_primary'])
        start_date_label.pack(side="left")

        start_date_entry = tk.Entry(start_date_frame,
                                  state="readonly",
                                  font=self.fonts['body'],
                                  bg=self.colors['light_gray'],
                                  fg=self.colors['text_primary'],
                                  relief="solid",
                                  bd=1,
                                  width=15,
                                  justify="center")
        start_date_entry.pack(side="right")

        # 截至日期
        end_date_frame = tk.Frame(date_frame, bg=self.colors['card_bg'])
        end_date_frame.pack(fill="x")

        end_date_label = tk.Label(end_date_frame,
                                text="📆 截至日期:",
                                font=self.fonts['body_bold'],
                                bg=self.colors['card_bg'],
                                fg=self.colors['text_primary'])
        end_date_label.pack(side="left")

        end_date_entry = tk.Entry(end_date_frame,
                                state="readonly",
                                font=self.fonts['body'],
                                bg=self.colors['light_gray'],
                                fg=self.colors['text_primary'],
                                relief="solid",
                                bd=1,
                                width=15,
                                justify="center")
        end_date_entry.pack(side="right")

        # 计算日期
        def update_dates(*args):
            today = datetime.now().date()
            start_date = today
            period = period_var.get()

            if period == "周租":
                end_date = today + timedelta(weeks=1)
            elif period == "月租":
                end_date = today + timedelta(days=30)
            elif period == "季租":
                end_date = today + timedelta(days=90)
            elif period == "半年租":
                end_date = today + timedelta(days=180)
            elif period == "年租":
                end_date = today + timedelta(days=365)

            start_date_entry.config(state="normal")
            start_date_entry.delete(0, tk.END)
            start_date_entry.insert(0, start_date.strftime("%Y-%m-%d"))
            start_date_entry.config(state="readonly")

            end_date_entry.config(state="normal")
            end_date_entry.delete(0, tk.END)
            end_date_entry.insert(0, end_date.strftime("%Y-%m-%d"))
            end_date_entry.config(state="readonly")

        period_var.trace_add("write", update_dates)
        update_dates()  # 初始化日期

        def on_confirm():
            # 完成租房流程
            rent_period = period_var.get()

            # 计算起租和截至日期
            today = datetime.now().date()
            start_date = today
            end_date = today

            if rent_period == "周租":
                end_date = today + timedelta(weeks=1)
            elif rent_period == "月租":
                end_date = today + timedelta(days=30)
            elif rent_period == "季租":
                end_date = today + timedelta(days=90)
            elif rent_period == "半年租":
                end_date = today + timedelta(days=180)
            elif rent_period == "年租":
                end_date = today + timedelta(days=365)
            else:
                messagebox.showerror("错误", "无效的租期选择")
                return

            # 首次租房时累加租金和押金到总额中
            # 电表读数不自动累加到总电量中
            rent_amount = room["rent"]
            deposit_amount = room["deposit"]
            electricity_amount = room.get("electricity_fee", 0)
            self.total_rent_collected += rent_amount
            self.total_deposit_collected += deposit_amount
            self.total_electricity_collected += electricity_amount  # 保留电费财务记录

            # 更新房间信息
            room["tenant_ids"] = [person["id"] for person in selected_persons]
            room["start_date"] = start_date.strftime("%Y-%m-%d")
            room["end_date"] = end_date.strftime("%Y-%m-%d")
            room["status"] = "已出租"

            self.save_data()
            self.refresh_cards()
            period_window.destroy()
            messagebox.showinfo("✅ 租房成功",
                              f"租房办理成功！\n\n"
                              f"收取租金: ¥{rent_amount:.0f}\n"
                              f"收取押金: ¥{deposit_amount:.0f}\n"
                              f"收取电费: ¥{electricity_amount:.0f}\n"
                              f"累计租金总额: ¥{self.total_rent_collected:.0f}\n"
                              f"累计押金总额: ¥{self.total_deposit_collected:.0f}\n"
                              f"累计电费总额: ¥{self.total_electricity_collected:.0f}")

        def on_cancel():
            period_window.destroy()

        # 按钮区域
        button_frame = tk.Frame(main_frame, bg=self.colors['background'])
        button_frame.pack(fill="x", pady=(10, 0))

        # 取消按钮
        cancel_btn = tk.Button(button_frame,
                             text="❌ 取消",
                             font=self.fonts['button'],
                             bg=self.colors['medium_gray'],
                             fg=self.colors['text_primary'],
                             relief="flat",
                             bd=0,
                             padx=25,
                             pady=10,
                             command=on_cancel,
                             cursor="hand2")
        cancel_btn.pack(side="right", padx=(10, 0))

        # 确认按钮
        save_btn = tk.Button(button_frame,
                           text="✅ 确认租房",
                           font=self.fonts['button'],
                           bg=self.colors['secondary'],
                           fg=self.colors['white'],
                           relief="flat",
                           bd=0,
                           padx=25,
                           pady=10,
                           command=on_confirm,
                           cursor="hand2")
        save_btn.pack(side="right")

        # 按钮悬停效果
        def on_save_enter(e):
            save_btn.configure(bg=self.colors['secondary_light'])
        def on_save_leave(e):
            save_btn.configure(bg=self.colors['secondary'])
        def on_cancel_enter(e):
            cancel_btn.configure(bg=self.colors['border'])
        def on_cancel_leave(e):
            cancel_btn.configure(bg=self.colors['medium_gray'])

        save_btn.bind("<Enter>", on_save_enter)
        save_btn.bind("<Leave>", on_save_leave)
        cancel_btn.bind("<Enter>", on_cancel_enter)
        cancel_btn.bind("<Leave>", on_cancel_leave)

    def add_person(self):
        """现代化的添加新人员对话框"""
        add_window = tk.Toplevel(self.root)
        add_window.title("👤 添加新人员")
        add_window.geometry("450x380")
        add_window.configure(bg=self.colors['background'])
        add_window.resizable(False, False)
        add_window.transient(self.root)
        add_window.grab_set()

        # 窗口居中
        add_window.update_idletasks()
        width = 450
        height = 380
        x = (add_window.winfo_screenwidth() // 2) - (width // 2)
        y = (add_window.winfo_screenheight() // 2) - (height // 2)
        add_window.geometry(f"{width}x{height}+{x}+{y}")

        # 主容器
        main_frame = tk.Frame(add_window, bg=self.colors['background'])
        main_frame.pack(fill="both", expand=True, padx=30, pady=20)

        # 标题区域
        title_frame = tk.Frame(main_frame, bg=self.colors['background'])
        title_frame.pack(fill="x", pady=(0, 25))

        title_label = tk.Label(title_frame,
                             text="添加新人员",
                             font=self.fonts['heading'],
                             bg=self.colors['background'],
                             fg=self.colors['primary'])
        title_label.pack()

        subtitle_label = tk.Label(title_frame,
                                text="请填写人员基本信息",
                                font=self.fonts['caption'],
                                bg=self.colors['background'],
                                fg=self.colors['text_secondary'])
        subtitle_label.pack(pady=(5, 0))

        # 表单区域
        form_frame = tk.Frame(main_frame, bg=self.colors['card_bg'], relief="flat", bd=1)
        form_frame.pack(fill="x", pady=(0, 20))

        # 内容容器
        content_frame = tk.Frame(form_frame, bg=self.colors['card_bg'])
        content_frame.pack(fill="x", padx=25, pady=25)

        # 姓名输入
        tk.Label(content_frame,
                text="姓名:",
                font=self.fonts['body_bold'],
                bg=self.colors['card_bg'],
                fg=self.colors['text_primary']).grid(row=0, column=0, padx=(0, 15), pady=(0, 15), sticky="e")

        name_entry = tk.Entry(content_frame,
                            font=self.fonts['body'],
                            bg=self.colors['white'],
                            fg=self.colors['text_primary'],
                            relief="solid",
                            bd=1,
                            width=20)
        name_entry.grid(row=0, column=1, pady=(0, 15), sticky="ew")

        # 身份证号输入
        tk.Label(content_frame,
                text="身份证号:",
                font=self.fonts['body_bold'],
                bg=self.colors['card_bg'],
                fg=self.colors['text_primary']).grid(row=1, column=0, padx=(0, 15), pady=(0, 15), sticky="e")

        id_card_entry = tk.Entry(content_frame,
                               font=self.fonts['body'],
                               bg=self.colors['white'],
                               fg=self.colors['text_primary'],
                               relief="solid",
                               bd=1,
                               width=20)
        id_card_entry.grid(row=1, column=1, pady=(0, 15), sticky="ew")

        # 电话输入
        tk.Label(content_frame,
                text="联系电话:",
                font=self.fonts['body_bold'],
                bg=self.colors['card_bg'],
                fg=self.colors['text_primary']).grid(row=2, column=0, padx=(0, 15), pady=(0, 15), sticky="e")

        phone_entry = tk.Entry(content_frame,
                             font=self.fonts['body'],
                             bg=self.colors['white'],
                             fg=self.colors['text_primary'],
                             relief="solid",
                             bd=1,
                             width=20)
        phone_entry.grid(row=2, column=1, pady=(0, 15), sticky="ew")

        content_frame.grid_columnconfigure(1, weight=1)

        # 按钮区域
        button_frame = tk.Frame(main_frame, bg=self.colors['background'])
        button_frame.pack(fill="x")

        def save_person():
            name = name_entry.get().strip()
            id_card = id_card_entry.get().strip()
            phone = phone_entry.get().strip()

            if not name:
                messagebox.showwarning("⚠️ 警告", "请输入姓名")
                name_entry.focus()
                return

            new_person = {
                "id": len(self.persons) + 1,
                "name": name,
                "id_card": id_card,
                "phone": phone
            }

            self.persons.append(new_person)
            self.save_data()
            messagebox.showinfo("✅ 成功", "新人员已添加")
            add_window.destroy()

        def cancel_add():
            add_window.destroy()

        # 取消按钮
        cancel_btn = tk.Button(button_frame,
                             text="❌ 取消",
                             font=self.fonts['button'],
                             bg=self.colors['danger'],
                             fg=self.colors['white'],
                             relief="flat",
                             bd=0,
                             padx=25,
                             pady=10,
                             command=cancel_add,
                             cursor="hand2")
        cancel_btn.pack(side="right", padx=(10, 0))

        # 保存按钮
        save_btn = tk.Button(button_frame,
                           text="✅ 保存",
                           font=self.fonts['button'],
                           bg=self.colors['secondary'],
                           fg=self.colors['white'],
                           relief="flat",
                           bd=0,
                           padx=25,
                           pady=10,
                           command=save_person,
                           cursor="hand2")
        save_btn.pack(side="right")

        # 按钮悬停效果
        def on_save_enter(e):
            save_btn.configure(bg=self.colors['secondary_light'])
        def on_save_leave(e):
            save_btn.configure(bg=self.colors['secondary'])
        def on_cancel_enter(e):
            cancel_btn.configure(bg=self.colors['danger_light'])
        def on_cancel_leave(e):
            cancel_btn.configure(bg=self.colors['danger'])

        save_btn.bind("<Enter>", on_save_enter)
        save_btn.bind("<Leave>", on_save_leave)
        cancel_btn.bind("<Enter>", on_cancel_enter)
        cancel_btn.bind("<Leave>", on_cancel_leave)

        # 设置焦点
        name_entry.focus()
    
    def show_all_persons(self):
        persons_window = tk.Toplevel(self.root)
        persons_window.title("所有人员信息")
        # 设置初始窗口大小
        persons_window.geometry("600x400")
        # 窗口居中
        persons_window.update_idletasks()
        width = persons_window.winfo_width()
        height = persons_window.winfo_height()
        x = (persons_window.winfo_screenwidth() // 2) - (width // 2)
        y = (persons_window.winfo_screenheight() // 2) - (height // 2)
        persons_window.geometry('{}x{}+{}+{}'.format(width, height, x, y))
        
        tree = ttk.Treeview(persons_window, columns=('id', 'name', 'id_card', 'phone'), show='headings')
        tree.heading('id', text='ID')
        tree.heading('name', text='姓名')
        tree.heading('id_card', text='身份证号')
        tree.heading('phone', text='联系电话')
        
        # 设置列宽
        tree.column('id', width=50)
        tree.column('name', width=100)
        tree.column('id_card', width=180)
        tree.column('phone', width=120)
        
        for person in self.persons:
            tree.insert('', 'end', values=(
                person['id'],
                person['name'],
                person['id_card'],
                person['phone']
            ))
        
        tree.pack(fill="both", expand=True, padx=10, pady=10)
    
    def load_data(self):
        try:
            if os.path.exists("rooms.json"):
                with open("rooms.json", "r", encoding='utf-8') as f:
                    self.rooms = json.load(f)
            else:
                self.rooms = []

            if os.path.exists("persons.json"):
                with open("persons.json", "r", encoding='utf-8') as f:
                    self.persons = json.load(f)
            else:
                self.persons = []

            # 加载累计财务数据
            if os.path.exists("financial_data.json"):
                with open("financial_data.json", "r", encoding='utf-8') as f:
                    financial_data = json.load(f)
                    self.total_rent_collected = financial_data.get("total_rent_collected", 0.0)
                    self.total_deposit_collected = financial_data.get("total_deposit_collected", 0.0)
                    self.total_electricity_collected = financial_data.get("total_electricity_collected", 0.0)
                    self.total_electricity_consumption = financial_data.get("total_electricity_consumption", 0.0)
            else:
                self.total_rent_collected = 0.0
                self.total_deposit_collected = 0.0
                self.total_electricity_collected = 0.0
                self.total_electricity_consumption = 0.0

            # 确保所有房间都有electricity_fee字段，并转换为整数
            for room in self.rooms:
                if 'electricity_fee' not in room:
                    room['electricity_fee'] = 0
                else:
                    # 确保电表读数为整数
                    room['electricity_fee'] = int(float(room['electricity_fee']))
        except Exception as e:
            messagebox.showerror("错误", f"加载数据失败: {e}")

    def save_data(self):
        try:
            with open("rooms.json", "w", encoding='utf-8') as f:
                json.dump(self.rooms, f, indent=2, ensure_ascii=False)

            with open("persons.json", "w", encoding='utf-8') as f:
                json.dump(self.persons, f, indent=2, ensure_ascii=False)

            # 保存累计财务数据
            financial_data = {
                "total_rent_collected": self.total_rent_collected,
                "total_deposit_collected": self.total_deposit_collected,
                "total_electricity_collected": self.total_electricity_collected,
                "total_electricity_consumption": self.total_electricity_consumption
            }
            with open("financial_data.json", "w", encoding='utf-8') as f:
                json.dump(financial_data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            messagebox.showerror("错误", f"保存数据失败: {e}")

class MultiFunctionWindow(tk.Toplevel):
    def __init__(self, parent, app, room):
        super().__init__(parent)
        self.app = app
        self.room = room
        self.title(f"🏠 房间管理 - {room['room_number']}")
        self.geometry("600x500")
        self.configure(bg=app.colors['background'])
        self.resizable(True, True)
        self.minsize(550, 450)
        self.transient(parent)
        self.grab_set()

        # 窗口居中
        self.update_idletasks()
        width = 600
        height = 500
        x = (self.winfo_screenwidth() // 2) - (width // 2)
        y = (self.winfo_screenheight() // 2) - (height // 2)
        self.geometry(f"{width}x{height}+{x}+{y}")

        # 创建现代化标题栏
        self.create_title_bar()

        # 创建现代化标签页
        self.notebook = ttk.Notebook(self, style="Modern.TNotebook")

        # 添加标签页 - 合并人员管理和房间信息
        self.integrated_info_tab = ttk.Frame(self.notebook)
        self.rent_tab = ttk.Frame(self.notebook)

        self.notebook.add(self.integrated_info_tab, text="📋 房间信息 & 人员管理")
        self.notebook.add(self.rent_tab, text="🏠 租赁管理")

        self.notebook.pack(expand=True, fill="both", padx=20, pady=(0, 20))

        # 初始化各标签页
        self.setup_integrated_info_tab()
        self.setup_rent_tab()

        # 根据房间状态禁用相关标签
        if self.room["status"] == "闲置":
            self.notebook.tab(1, state="disabled")  # 禁用租赁管理标签

    def create_title_bar(self):
        """创建现代化标题栏"""
        title_frame = tk.Frame(self, bg=self.app.colors['primary'], height=60)
        title_frame.pack(fill="x")
        title_frame.pack_propagate(False)

        # 标题内容
        content_frame = tk.Frame(title_frame, bg=self.app.colors['primary'])
        content_frame.pack(fill="both", expand=True, padx=20, pady=15)

        # 房间图标和标题
        icon_label = tk.Label(content_frame,
                            text="🏠",
                            font=("Segoe UI Emoji", 20),
                            bg=self.app.colors['primary'],
                            fg=self.app.colors['white'])
        icon_label.pack(side="left")

        title_label = tk.Label(content_frame,
                             text=f"房间 {self.room['room_number']} 管理",
                             font=self.app.fonts['heading'],
                             bg=self.app.colors['primary'],
                             fg=self.app.colors['white'])
        title_label.pack(side="left", padx=(10, 0))

        # 状态指示器
        status_text = "闲置" if self.room["status"] == "闲置" else "租赁中"
        status_color = self.app.colors['warning'] if self.room["status"] == "闲置" else self.app.colors['secondary']

        status_label = tk.Label(content_frame,
                              text=f"● {status_text}",
                              font=self.app.fonts['body_bold'],
                              bg=self.app.colors['primary'],
                              fg=status_color)
        status_label.pack(side="right")
    
    def setup_integrated_info_tab(self):
        # 创建现代化滚动容器框架
        container_frame = tk.Frame(self.integrated_info_tab, bg=self.app.colors['background'])
        container_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # 创建现代化Canvas和滚动条
        self.info_canvas = tk.Canvas(container_frame,
                                   bg=self.app.colors['background'],
                                   highlightthickness=0,
                                   relief="flat",
                                   bd=0)

        # 创建现代化滚动条样式
        style = ttk.Style()
        style.configure("InfoTab.Vertical.TScrollbar",
                       background=self.app.colors['medium_gray'],
                       troughcolor=self.app.colors['light_gray'],
                       bordercolor=self.app.colors['border'],
                       arrowcolor=self.app.colors['text_secondary'],
                       darkcolor=self.app.colors['border'],
                       lightcolor=self.app.colors['white'],
                       borderwidth=0,
                       relief="flat")

        # 创建滚动条
        self.info_scrollbar = ttk.Scrollbar(container_frame, orient="vertical",
                                          command=self.info_canvas.yview,
                                          style="InfoTab.Vertical.TScrollbar")
        self.info_canvas.configure(yscrollcommand=self.info_scrollbar.set)

        # 创建可滚动的内容框架
        self.info_scrollable_frame = tk.Frame(self.info_canvas, bg=self.app.colors['background'])
        self.info_canvas_window = self.info_canvas.create_window((0, 0), window=self.info_scrollable_frame, anchor="nw")

        # 创建主容器（在可滚动框架内）
        main_frame = tk.Frame(self.info_scrollable_frame, bg=self.app.colors['background'])
        main_frame.pack(fill="both", expand=True, padx=15, pady=15)

        # 创建左右分栏布局 - 设置固定宽度避免文字溢出
        left_frame = tk.Frame(main_frame, bg=self.app.colors['background'])
        left_frame.pack(side="left", fill="both", expand=True, padx=(0, 10))

        right_frame = tk.Frame(main_frame, bg=self.app.colors['background'], width=280)
        right_frame.pack(side="right", fill="y", padx=(10, 0))
        right_frame.pack_propagate(False)  # 保持固定宽度

        # 房间信息区域（左侧）- 现代化卡片设计
        room_card = tk.Frame(left_frame,
                           bg=self.app.colors['card_bg'],
                           relief="flat",
                           bd=1,
                           highlightthickness=1,
                           highlightcolor=self.app.colors['border'],
                           highlightbackground=self.app.colors['border'])
        room_card.pack(fill="x", pady=(0, 15))

        # 卡片标题
        room_title_frame = tk.Frame(room_card, bg=self.app.colors['card_bg'])
        room_title_frame.pack(fill="x", padx=20, pady=(15, 10))

        room_title_icon = tk.Label(room_title_frame,
                                 text="🏠",
                                 font=("Segoe UI Emoji", 14),
                                 bg=self.app.colors['card_bg'])
        room_title_icon.pack(side="left")

        room_title_label = tk.Label(room_title_frame,
                                  text="房间信息",
                                  font=self.app.fonts['subheading'],
                                  bg=self.app.colors['card_bg'],
                                  fg=self.app.colors['primary'])
        room_title_label.pack(side="left", padx=(8, 0))

        # 房间信息内容
        room_content = tk.Frame(room_card, bg=self.app.colors['card_bg'])
        room_content.pack(fill="x", padx=20, pady=(0, 20))

        # 房号
        self.create_info_row(room_content, "房号:", self.room["room_number"], 0)

        # 租金
        self.create_info_row(room_content, "租金:", f"¥{self.room['rent']:.0f}", 1)

        # 押金
        self.create_info_row(room_content, "押金:", f"¥{self.room['deposit']:.0f}", 2)

        # 电表
        self.create_info_row(room_content, "电表:", f"{int(self.room.get('electricity_fee', 0))} kWh", 3)

        room_content.grid_columnconfigure(1, weight=1)

        # 人员管理区域（左侧下方）- 现代化卡片设计
        person_card = tk.Frame(left_frame,
                             bg=self.app.colors['card_bg'],
                             relief="flat",
                             bd=1,
                             highlightthickness=1,
                             highlightcolor=self.app.colors['border'],
                             highlightbackground=self.app.colors['border'])
        person_card.pack(fill="x", pady=(15, 0))

        # 卡片标题
        person_title_frame = tk.Frame(person_card, bg=self.app.colors['card_bg'])
        person_title_frame.pack(fill="x", padx=20, pady=(15, 10))

        person_title_icon = tk.Label(person_title_frame,
                                   text="👤",
                                   font=("Segoe UI Emoji", 14),
                                   bg=self.app.colors['card_bg'])
        person_title_icon.pack(side="left")

        person_title_label = tk.Label(person_title_frame,
                                    text="人员登记",
                                    font=self.app.fonts['subheading'],
                                    bg=self.app.colors['card_bg'],
                                    fg=self.app.colors['primary'])
        person_title_label.pack(side="left", padx=(8, 0))

        # 人员注册表单内容
        person_content = tk.Frame(person_card, bg=self.app.colors['card_bg'])
        person_content.pack(fill="x", padx=20, pady=(0, 20))

        # 姓名输入
        tk.Label(person_content,
                text="姓名:",
                font=self.app.fonts['body_bold'],
                bg=self.app.colors['card_bg'],
                fg=self.app.colors['text_secondary']).grid(row=0, column=0, padx=(0, 15), pady=8, sticky="e")

        self.name_entry = tk.Entry(person_content,
                                 font=self.app.fonts['body'],
                                 bg=self.app.colors['white'],
                                 fg=self.app.colors['text_primary'],
                                 relief="solid",
                                 bd=1,
                                 width=20)
        self.name_entry.grid(row=0, column=1, pady=8, sticky="ew")

        # 身份证号输入
        tk.Label(person_content,
                text="身份证号:",
                font=self.app.fonts['body_bold'],
                bg=self.app.colors['card_bg'],
                fg=self.app.colors['text_secondary']).grid(row=1, column=0, padx=(0, 15), pady=8, sticky="e")

        self.id_card_entry = tk.Entry(person_content,
                                    font=self.app.fonts['body'],
                                    bg=self.app.colors['white'],
                                    fg=self.app.colors['text_primary'],
                                    relief="solid",
                                    bd=1,
                                    width=20)
        self.id_card_entry.grid(row=1, column=1, pady=8, sticky="ew")

        # 联系电话输入
        tk.Label(person_content,
                text="联系电话:",
                font=self.app.fonts['body_bold'],
                bg=self.app.colors['card_bg'],
                fg=self.app.colors['text_secondary']).grid(row=2, column=0, padx=(0, 15), pady=8, sticky="e")

        self.phone_entry = tk.Entry(person_content,
                                  font=self.app.fonts['body'],
                                  bg=self.app.colors['white'],
                                  fg=self.app.colors['text_primary'],
                                  relief="solid",
                                  bd=1,
                                  width=20)
        self.phone_entry.grid(row=2, column=1, pady=8, sticky="ew")

        # 一键入住按钮（仅在房间闲置时显示）
        if self.room["status"] == "闲置":
            register_btn = tk.Button(person_content,
                                   text="🏠 注册并入住",
                                   font=self.app.fonts['button'],
                                   bg=self.app.colors['primary'],
                                   fg=self.app.colors['white'],
                                   relief="flat",
                                   bd=0,
                                   padx=15,
                                   pady=8,
                                   command=self.register_person,
                                   cursor="hand2")
        else:
            # 如果房间已出租，只显示注册按钮
            register_btn = tk.Button(person_content,
                                   text="📝 注册人员",
                                   font=self.app.fonts['button'],
                                   bg=self.app.colors['secondary'],
                                   fg=self.app.colors['white'],
                                   relief="flat",
                                   bd=0,
                                   padx=15,
                                   pady=8,
                                   command=self.register_person,
                                   cursor="hand2")
        register_btn.grid(row=3, column=0, columnspan=2, pady=(15, 0), sticky="ew")

        person_content.grid_columnconfigure(1, weight=1)

        # 快捷操作区域（右侧）- 现代化卡片设计
        action_card = tk.Frame(right_frame,
                             bg=self.app.colors['card_bg'],
                             relief="flat",
                             bd=1,
                             highlightthickness=1,
                             highlightcolor=self.app.colors['border'],
                             highlightbackground=self.app.colors['border'])
        action_card.pack(fill="both", expand=True)

        # 卡片标题 - 优化布局防止文字溢出
        action_title_frame = tk.Frame(action_card, bg=self.app.colors['card_bg'])
        action_title_frame.pack(fill="x", padx=15, pady=(15, 10))

        action_title_icon = tk.Label(action_title_frame,
                                   text="⚡",
                                   font=("Segoe UI Emoji", 12),  # 稍微减小图标字体
                                   bg=self.app.colors['card_bg'])
        action_title_icon.pack(side="left")

        action_title_label = tk.Label(action_title_frame,
                                    text="快捷操作",
                                    font=self.app.fonts['body_bold'],  # 使用稍小的字体
                                    bg=self.app.colors['card_bg'],
                                    fg=self.app.colors['primary'],
                                    wraplength=200)  # 设置文字换行长度
        action_title_label.pack(side="left", padx=(6, 0), fill="x", expand=True)

        # 操作内容
        action_content = tk.Frame(action_card, bg=self.app.colors['card_bg'])
        action_content.pack(fill="both", expand=True, padx=15, pady=(0, 15))

        # 租房相关操作
        if self.room["status"] == "闲置":
            # 办理租房按钮
            quick_rent_btn = tk.Button(action_content,
                                     text="🏠 办理租房",
                                     font=self.app.fonts['button'],
                                     bg=self.app.colors['primary'],
                                     fg=self.app.colors['white'],
                                     relief="flat",
                                     bd=0,
                                     padx=15,
                                     pady=10,
                                     command=self.quick_rent_workflow,
                                     cursor="hand2")
            quick_rent_btn.pack(fill="x", pady=5)
        else:
            # 显示当前租户信息
            tenant_info_frame = tk.Frame(action_content, bg=self.app.colors['card_bg'])
            tenant_info_frame.pack(fill="x", pady=5)

            tk.Label(tenant_info_frame,
                    text="当前租户:",
                    font=self.app.fonts['body_bold'],
                    bg=self.app.colors['card_bg'],
                    fg=self.app.colors['text_secondary']).pack(anchor="w")

            tenant_names = []
            for tenant_id in self.room.get("tenant_ids", []):
                tenant = next((p for p in self.app.persons if p["id"] == tenant_id), None)
                if tenant:
                    tenant_names.append(tenant['name'])
            tenant_display = ", ".join(tenant_names) if tenant_names else "无"

            tk.Label(tenant_info_frame,
                    text=tenant_display,
                    font=self.app.fonts['body'],
                    bg=self.app.colors['card_bg'],
                    fg=self.app.colors['text_primary'],
                    wraplength=150).pack(anchor="w", pady=(5, 0))

            # 房间已出租状态提示
            status_label = tk.Label(action_content,
                                  text="🔒 房间已出租",
                                  font=self.app.fonts['body_bold'],
                                  bg=self.app.colors['card_bg'],
                                  fg=self.app.colors['warning'])
            status_label.pack(pady=10)

        # 配置滚动区域
        self.info_scrollable_frame.bind('<Configure>', self.on_info_frame_configure)
        self.info_canvas.bind('<Configure>', self.on_info_canvas_configure)

        # 绑定鼠标滚轮事件
        self.bind_mousewheel_to_info_canvas()

        # 布局Canvas和滚动条
        self.info_canvas.pack(side="left", fill="both", expand=True)
        self.info_scrollbar.pack(side="right", fill="y")

        # 初始化滚动条可见性
        self.after(100, self.update_info_scrollbar_visibility)

    def create_info_row(self, parent, label_text, value_text, row):
        """创建现代化信息行"""
        # 标签
        label = tk.Label(parent,
                        text=label_text,
                        font=self.app.fonts['body_bold'],
                        bg=self.app.colors['card_bg'],
                        fg=self.app.colors['text_secondary'])
        label.grid(row=row, column=0, padx=(0, 15), pady=8, sticky="e")

        # 值
        value_frame = tk.Frame(parent,
                             bg=self.app.colors['white'],
                             relief="solid",
                             bd=1)
        value_frame.grid(row=row, column=1, pady=8, sticky="ew")

        value_label = tk.Label(value_frame,
                             text=value_text,
                             font=self.app.fonts['body'],
                             bg=self.app.colors['white'],
                             fg=self.app.colors['text_primary'])
        value_label.pack(padx=10, pady=6)











    def on_info_frame_configure(self, event):
        """当可滚动框架大小改变时更新滚动区域"""
        self.info_canvas.configure(scrollregion=self.info_canvas.bbox("all"))
        self.update_info_scrollbar_visibility()

    def on_info_canvas_configure(self, event):
        """当Canvas大小改变时调整内容框架宽度"""
        canvas_width = event.width
        self.info_canvas.itemconfig(self.info_canvas_window, width=canvas_width)

    def bind_mousewheel_to_info_canvas(self):
        """绑定鼠标滚轮事件到Canvas"""
        def on_mousewheel(event):
            # 检查滚动条是否可见，只有可见时才响应滚轮
            if self.info_scrollbar.winfo_viewable():
                self.info_canvas.yview_scroll(int(-1 * (event.delta / 120)), "units")

        # 绑定到Canvas和其所有子组件
        def bind_to_mousewheel(widget):
            widget.bind("<MouseWheel>", on_mousewheel)
            for child in widget.winfo_children():
                bind_to_mousewheel(child)

        bind_to_mousewheel(self.info_canvas)
        bind_to_mousewheel(self.info_scrollable_frame)

    def update_info_scrollbar_visibility(self):
        """根据内容高度自动显示/隐藏滚动条"""
        try:
            self.info_canvas.update_idletasks()

            # 获取Canvas和内容的高度
            canvas_height = self.info_canvas.winfo_height()
            content_height = self.info_scrollable_frame.winfo_reqheight()

            # 如果内容高度大于Canvas高度，显示滚动条
            if content_height > canvas_height and canvas_height > 1:
                self.info_scrollbar.pack(side="right", fill="y")
            else:
                self.info_scrollbar.pack_forget()

        except tk.TclError:
            # 窗口可能已经被销毁，忽略错误
            pass

    def register_person(self):
        """注册新人员"""
        try:
            # 获取输入值
            name = self.name_entry.get().strip()
            id_card = self.id_card_entry.get().strip()
            phone = self.phone_entry.get().strip()

            # 验证输入
            if not name:
                messagebox.showwarning("⚠️ 警告", "请输入姓名")
                return
            if not id_card:
                messagebox.showwarning("⚠️ 警告", "请输入身份证号")
                return
            if not phone:
                messagebox.showwarning("⚠️ 警告", "请输入联系电话")
                return

            # 检查是否已存在相同身份证号的人员
            for person in self.app.persons:
                if person.get('id_card') == id_card:
                    messagebox.showwarning("⚠️ 警告", "该身份证号已存在，请检查输入")
                    return

            # 创建新人员
            new_person = {
                "id": len(self.app.persons) + 1,
                "name": name,
                "id_card": id_card,
                "phone": phone
            }

            # 添加到人员列表
            self.app.persons.append(new_person)
            self.app.save_data()

            # 清空输入框
            self.name_entry.delete(0, tk.END)
            self.id_card_entry.delete(0, tk.END)
            self.phone_entry.delete(0, tk.END)

            # 如果房间是闲置状态，自动办理入住手续
            if self.room["status"] == "闲置":
                # 直接触发自动入住流程
                self.auto_rent_for_person(new_person)
            else:
                # 如果房间已出租，只显示注册成功消息
                messagebox.showinfo("✅ 注册成功", f"人员 {name} 注册成功！")

        except Exception as e:
            messagebox.showerror("❌ 错误", f"注册失败: {e}")

    def auto_rent_for_person(self, person):
        """为指定人员自动办理入住手续"""
        try:
            # 检查房间是否已出租
            if self.room["status"] == "已出租":
                messagebox.showwarning("⚠️ 警告", "该房间已出租，无法重复办理入住手续")
                return

            # 设置默认租期为1个月
            from datetime import datetime, timedelta
            start_date = datetime.now()
            end_date = start_date + timedelta(days=30)

            # 更新房间信息
            rent_amount = self.room["rent"]
            deposit_amount = self.room["deposit"]
            electricity_amount = self.room.get("electricity_fee", 0)

            # 更新财务统计
            self.app.total_rent_collected += rent_amount
            self.app.total_deposit_collected += deposit_amount
            self.app.total_electricity_collected += electricity_amount

            # 更新房间状态
            self.room["tenant_ids"] = [person["id"]]
            self.room["start_date"] = start_date.strftime("%Y-%m-%d")
            self.room["end_date"] = end_date.strftime("%Y-%m-%d")
            self.room["status"] = "已出租"

            # 保存数据并刷新界面
            self.app.save_data()
            self.app.refresh_cards()

            # 显示成功信息
            messagebox.showinfo("✅ 入住成功",
                              f"自动入住办理成功！\n\n"
                              f"租户: {person['name']}\n"
                              f"房间: {self.room['room_number']}\n"
                              f"租期: 1个月\n"
                              f"开始日期: {start_date.strftime('%Y-%m-%d')}\n"
                              f"结束日期: {end_date.strftime('%Y-%m-%d')}\n"
                              f"收取租金: ¥{rent_amount:.0f}\n"
                              f"收取押金: ¥{deposit_amount:.0f}")

            # 关闭房间管理窗口
            self.destroy()

        except Exception as e:
            messagebox.showerror("❌ 错误", f"自动入住失败: {e}")

    def quick_rent_workflow(self):
        """快速租房工作流程 - 支持自动注册和直接租房"""
        try:
            # 检查是否填写了人员信息
            name = self.name_entry.get().strip()
            id_card = self.id_card_entry.get().strip()
            phone = self.phone_entry.get().strip()

            # 如果填写了人员信息，进行自动注册流程
            if name or id_card or phone:
                # 验证所有字段都已填写
                if not name:
                    messagebox.showwarning("⚠️ 警告", "请输入姓名")
                    return
                if not id_card:
                    messagebox.showwarning("⚠️ 警告", "请输入身份证号")
                    return
                if not phone:
                    messagebox.showwarning("⚠️ 警告", "请输入联系电话")
                    return

                # 检查是否已存在相同身份证号的人员
                existing_person = None
                for person in self.app.persons:
                    if person.get('id_card') == id_card:
                        existing_person = person
                        break

                # 如果不存在，自动注册新人员
                if not existing_person:
                    new_person = {
                        "id": len(self.app.persons) + 1,
                        "name": name,
                        "id_card": id_card,
                        "phone": phone
                    }
                    self.app.persons.append(new_person)
                    self.app.save_data()
                    selected_person = new_person
                else:
                    # 使用已存在的人员
                    selected_person = existing_person

                # 清空输入框
                self.name_entry.delete(0, tk.END)
                self.id_card_entry.delete(0, tk.END)
                self.phone_entry.delete(0, tk.END)

                # 直接进入租期选择，跳过租户选择步骤
                self.direct_rent_with_person([selected_person])
            else:
                # 如果没有填写人员信息，使用原有流程
                self.destroy()
                self.app.show_tenant_selection_window(self.room)

        except Exception as e:
            messagebox.showerror("❌ 错误", f"操作失败: {e}")

    def direct_rent_with_person(self, selected_persons):
        """直接租房流程 - 跳过租户选择，直接进入租期选择"""
        # 创建租期选择对话框
        period_dialog = tk.Toplevel(self)
        period_dialog.title("选择租期")
        period_dialog.geometry("350x250")
        period_dialog.transient(self)
        period_dialog.grab_set()

        # 窗口居中
        period_dialog.update_idletasks()
        width = period_dialog.winfo_width()
        height = period_dialog.winfo_height()
        x = (period_dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (period_dialog.winfo_screenheight() // 2) - (height // 2)
        period_dialog.geometry(f"{width}x{height}+{x}+{y}")

        # 内容
        main_frame = ttk.Frame(period_dialog, padding=20)
        main_frame.pack(fill="both", expand=True)

        # 显示租户信息
        ttk.Label(main_frame, text="租户信息:", font=('Arial', 12, 'bold')).pack(pady=(0, 5))
        tenant_info = f"姓名: {selected_persons[0]['name']}\n身份证: {selected_persons[0]['id_card']}\n电话: {selected_persons[0]['phone']}"
        ttk.Label(main_frame, text=tenant_info, font=('Arial', 10),
                 relief="sunken", padding=10).pack(pady=(0, 15), fill="x")

        ttk.Label(main_frame, text="选择租期类型:", font=('Arial', 12, 'bold')).pack(pady=(0, 10))

        period_var = tk.StringVar(value="月租")
        period_combobox = ttk.Combobox(main_frame, textvariable=period_var,
                                      values=["周租", "月租", "季租", "半年租", "年租"],
                                      state="readonly", width=20)
        period_combobox.pack(pady=(0, 20))

        def confirm_rent():
            rent_period = period_var.get()
            period_dialog.destroy()

            # 执行租房逻辑
            from datetime import datetime, timedelta
            today = datetime.now().date()
            start_date = today

            if rent_period == "周租":
                end_date = today + timedelta(weeks=1)
            elif rent_period == "月租":
                end_date = today + timedelta(days=30)
            elif rent_period == "季租":
                end_date = today + timedelta(days=90)
            elif rent_period == "半年租":
                end_date = today + timedelta(days=180)
            elif rent_period == "年租":
                end_date = today + timedelta(days=365)

            # 更新房间信息
            rent_amount = self.room["rent"]
            deposit_amount = self.room["deposit"]
            electricity_amount = self.room.get("electricity_fee", 0)

            self.app.total_rent_collected += rent_amount
            self.app.total_deposit_collected += deposit_amount
            self.app.total_electricity_collected += electricity_amount

            self.room["tenant_ids"] = [person["id"] for person in selected_persons]
            self.room["start_date"] = start_date.strftime("%Y-%m-%d")
            self.room["end_date"] = end_date.strftime("%Y-%m-%d")
            self.room["status"] = "已出租"

            self.app.save_data()
            self.app.refresh_cards()

            messagebox.showinfo("✅ 租房成功",
                              f"租房办理成功！\n\n"
                              f"租户: {', '.join([p['name'] for p in selected_persons])}\n"
                              f"租期: {rent_period}\n"
                              f"收取租金: ¥{rent_amount:.0f}\n"
                              f"收取押金: ¥{deposit_amount:.0f}\n"
                              f"收取电费: ¥{electricity_amount:.0f}")

            # 关闭房间管理窗口
            self.destroy()

        # 按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill="x")

        ttk.Button(button_frame, text="取消",
                  command=period_dialog.destroy).pack(side="right", padx=(10, 0))
        ttk.Button(button_frame, text="确认租房",
                  command=confirm_rent).pack(side="right")

    def quick_rent_with_person(self, selected_persons):
        """使用指定人员快速租房"""
        # 创建简化的租期选择对话框
        period_dialog = tk.Toplevel(self)
        period_dialog.title("选择租期")
        period_dialog.geometry("300x200")
        period_dialog.transient(self)
        period_dialog.grab_set()

        # 窗口居中
        period_dialog.update_idletasks()
        width = period_dialog.winfo_width()
        height = period_dialog.winfo_height()
        x = (period_dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (period_dialog.winfo_screenheight() // 2) - (height // 2)
        period_dialog.geometry(f"{width}x{height}+{x}+{y}")

        # 内容
        main_frame = ttk.Frame(period_dialog, padding=20)
        main_frame.pack(fill="both", expand=True)

        ttk.Label(main_frame, text="选择租期类型:", font=('Arial', 12, 'bold')).pack(pady=10)

        period_var = tk.StringVar(value="月租")
        period_combobox = ttk.Combobox(main_frame, textvariable=period_var,
                                      values=["周租", "月租", "季租", "半年租", "年租"],
                                      state="readonly", width=20)
        period_combobox.pack(pady=10)

        def confirm_rent():
            rent_period = period_var.get()
            period_dialog.destroy()

            # 执行租房逻辑
            from datetime import datetime, timedelta
            today = datetime.now().date()
            start_date = today

            if rent_period == "周租":
                end_date = today + timedelta(weeks=1)
            elif rent_period == "月租":
                end_date = today + timedelta(days=30)
            elif rent_period == "季租":
                end_date = today + timedelta(days=90)
            elif rent_period == "半年租":
                end_date = today + timedelta(days=180)
            elif rent_period == "年租":
                end_date = today + timedelta(days=365)

            # 更新房间信息
            rent_amount = self.room["rent"]
            deposit_amount = self.room["deposit"]
            electricity_amount = self.room.get("electricity_fee", 0)

            self.app.total_rent_collected += rent_amount
            self.app.total_deposit_collected += deposit_amount
            self.app.total_electricity_collected += electricity_amount

            self.room["tenant_ids"] = [person["id"] for person in selected_persons]
            self.room["start_date"] = start_date.strftime("%Y-%m-%d")
            self.room["end_date"] = end_date.strftime("%Y-%m-%d")
            self.room["status"] = "已出租"

            self.app.save_data()
            self.app.refresh_cards()

            messagebox.showinfo("✅ 租房成功",
                              f"快速租房办理成功！\n\n"
                              f"租户: {', '.join([p['name'] for p in selected_persons])}\n"
                              f"租期: {rent_period}\n"
                              f"收取租金: ¥{rent_amount:.0f}\n"
                              f"收取押金: ¥{deposit_amount:.0f}")

            # 关闭房间管理窗口
            self.destroy()

        ttk.Button(main_frame, text="确认租房", command=confirm_rent).pack(pady=20)


    def setup_rent_tab(self):
        # 租赁管理界面
        frame = ttk.Frame(self.rent_tab)
        frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        if self.room["status"] == "闲置":
            ttk.Label(frame, text="当前房间闲置，请先办理租房", 
                     font=('Arial', 12, 'bold')).pack(pady=50)
            return
            
        # 显示当前租赁信息
        ttk.Label(frame, text="当前租户:").grid(row=0, column=0, padx=5, pady=5, sticky="e")
        tenant_names = []
        for tenant_id in self.room.get("tenant_ids", []):
            tenant = next((p for p in self.app.persons if p["id"] == tenant_id), None)
            if tenant:
                tenant_names.append(tenant['name'])
        tenant_display = ", ".join(tenant_names) if tenant_names else "无"
        ttk.Label(frame, text=tenant_display).grid(row=0, column=1, padx=5, pady=5, sticky="w")
        
        # 安全显示日期
        start_date = self.room.get("start_date", "未设置")
        end_date = self.room.get("end_date", "未设置")
        
        ttk.Label(frame, text="起租日期:").grid(row=1, column=0, padx=5, pady=5, sticky="e")
        ttk.Label(frame, text=start_date).grid(row=1, column=1, padx=5, pady=5, sticky="w")
        
        ttk.Label(frame, text="截至日期:").grid(row=2, column=0, padx=5, pady=5, sticky="e")
        ttk.Label(frame, text=end_date).grid(row=2, column=1, padx=5, pady=5, sticky="w")
        
        # 续租选项
        ttk.Label(frame, text="续租类型:").grid(row=3, column=0, padx=5, pady=5, sticky="e")
        self.period_var = tk.StringVar(value="月租")
        period_combobox = ttk.Combobox(frame, textvariable=self.period_var, 
                                      values=["周租", "月租", "季租", "半年租", "年租"], 
                                      state="readonly")
        period_combobox.grid(row=3, column=1, padx=5, pady=5, sticky="ew")
        
        ttk.Label(frame, text="新截至日期:").grid(row=4, column=0, padx=5, pady=5, sticky="e")
        self.new_end_date_var = tk.StringVar()
        ttk.Label(frame, textvariable=self.new_end_date_var).grid(row=4, column=1, padx=5, pady=5, sticky="w")
        
        # 计算新截止日期
        def update_new_end_date(*args):
            try:
                current_end_date = datetime.strptime(self.room['end_date'], "%Y-%m-%d").date()
                period = self.period_var.get()
                
                if period == "周租":
                    new_end_date = current_end_date + timedelta(weeks=1)
                elif period == "月租":
                    new_end_date = current_end_date + timedelta(days=30)
                elif period == "季租":
                    new_end_date = current_end_date + timedelta(days=90)
                elif period == "半年租":
                    new_end_date = current_end_date + timedelta(days=180)
                elif period == "年租":
                    new_end_date = current_end_date + timedelta(days=365)
                
                self.new_end_date_var.set(new_end_date.strftime("%Y-%m-%d"))
            except Exception as e:
                messagebox.showerror("错误", f"计算日期时出错: {e}")
                self.new_end_date_var.set("计算错误")
        
        self.period_var.trace_add("write", update_new_end_date)
        update_new_end_date()  # 初始化日期
        
        # 操作按钮
        btn_frame = ttk.Frame(frame)
        btn_frame.grid(row=5, column=0, columnspan=2, pady=10)
        
        ttk.Button(btn_frame, text="确认续租", command=self.confirm_extend).pack(side="left", padx=5)
        ttk.Button(btn_frame, text="办理退房", command=self.check_out).pack(side="left", padx=5)
        
        frame.grid_columnconfigure(1, weight=1)
    
    def confirm_extend(self):
        try:
            # 验证日期有效性
            new_end_date = self.new_end_date_var.get()
            if not new_end_date or new_end_date == "计算错误":
                raise ValueError("无效的截止日期")

            # 尝试解析日期以验证格式
            datetime.strptime(new_end_date, "%Y-%m-%d")

            # 续租时累加租金到总额中
            # 电表读数不自动累加到总电量中
            rent_amount = self.room["rent"]
            electricity_amount = self.room.get("electricity_fee", 0)
            self.app.total_rent_collected += rent_amount
            self.app.total_electricity_collected += electricity_amount  # 保留电费财务记录

            # 更新房间截止日期
            self.room["end_date"] = new_end_date

            self.app.save_data()
            self.app.refresh_cards()
            messagebox.showinfo("✅ 续租成功",
                              f"续租办理成功！\n\n"
                              f"本次续租收取租金: ¥{rent_amount:.0f}\n"
                              f"本次续租收取电费: ¥{electricity_amount:.0f}\n"
                              f"累计租金总额: ¥{self.app.total_rent_collected:.0f}\n"
                              f"累计电费总额: ¥{self.app.total_electricity_collected:.0f}")
        except ValueError as ve:
            messagebox.showerror("❌ 错误", f"日期格式无效: {ve}")
        except Exception as e:
            messagebox.showerror("❌ 错误", f"续租办理失败: {e}")
    
    def check_out(self):
        if messagebox.askyesno("⚠️ 确认退房", "确定要办理退房吗？\n\n退房后将从押金总额中减去相应金额。"):
            # 退房时从押金总额中减去相应金额
            deposit_amount = self.room["deposit"]
            self.app.total_deposit_collected -= deposit_amount

            # 确保押金总额不为负数
            if self.app.total_deposit_collected < 0:
                self.app.total_deposit_collected = 0

            self.room["tenant_ids"] = []
            self.room["start_date"] = None
            self.room["end_date"] = None
            self.room["status"] = "闲置"

            self.app.save_data()
            self.app.refresh_cards()
            messagebox.showinfo("✅ 退房成功",
                              f"退房办理成功！\n\n"
                              f"退还押金: ¥{deposit_amount:.0f}\n"
                              f"剩余押金总额: ¥{self.app.total_deposit_collected:.0f}")
            self.destroy()
    



if __name__ == "__main__":
    root = tk.Tk()
    root.title("房间管理系统")
    # 主界面居中
    root.update_idletasks()
    width = root.winfo_width()
    height = root.winfo_height()
    x = (root.winfo_screenwidth() // 2) - (width // 2)
    y = (root.winfo_screenheight() // 2) - (height // 2)
    root.geometry('{}x{}+{}+{}'.format(width, height, x, y))
    app = HotelManagementSystem(root)
    root.mainloop()