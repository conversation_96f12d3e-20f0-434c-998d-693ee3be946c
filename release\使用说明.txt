🏨 房间管理系统 v2.0 使用说明
========================================

📋 系统概述
本系统是一个现代化的房间租赁管理软件，适用于酒店、公寓、宿舍等场所的房间管理。

🔧 系统要求
- Windows 7/8/10/11 (64位)
- 无需安装Python环境
- 建议屏幕分辨率：1200x800或更高

📁 文件说明
房间管理系统.exe     - 主程序文件
rooms.json          - 房间信息数据文件
persons.json        - 租户信息数据文件  
financial_data.json - 财务统计数据文件
使用说明.txt        - 本说明文件

🚀 安装和运行
1. 将所有文件解压到任意目录
2. 双击"房间管理系统.exe"启动程序
3. 首次运行会自动初始化数据文件

✨ 主要功能
📊 房间管理
- 查看所有房间状态（闲置/已出租）
- 添加、编辑、删除房间信息
- 设置房间租金、押金、电费等

👥 租户管理  
- 添加、编辑、删除租户信息
- 管理租户身份证、电话等资料
- 右键菜单快速操作

🏠 租房流程
- 选择闲置房间进行出租
- 选择租户并设置租期
- 支持月租、季租、半年租、年租
- 自动计算起租和截止日期

💰 财务统计
- 实时显示租金、押金、电费收入
- 自动统计总收入和各项明细
- 财务数据持久化保存

🎯 操作指南

启动程序
1. 双击"房间管理系统.exe"
2. 等待程序加载完成
3. 主界面显示所有房间卡片

添加房间
1. 点击"添加房间"按钮
2. 填写房间号、租金、押金等信息
3. 点击"保存"完成添加

添加租户
1. 点击"人员管理"按钮
2. 点击"添加人员"
3. 填写姓名、身份证、电话等信息
4. 点击"保存"完成添加

租房操作
1. 点击闲置房间（蓝色卡片）
2. 在弹出菜单中选择"出租房间"
3. 选择租户（可多选）
4. 设置租期类型和起租日期
5. 点击"确认租房"完成

退房操作
1. 点击已出租房间（绿色卡片）
2. 在弹出菜单中选择"退房"
3. 确认退房操作

续租操作
1. 点击已出租房间
2. 选择"续租"
3. 设置新的租期
4. 确认续租

编辑信息
1. 右键点击房间卡片
2. 选择"编辑房间信息"
3. 修改相关信息并保存

⚠️ 注意事项

数据安全
- 请定期备份数据文件（*.json）
- 不要手动编辑数据文件
- 建议在关闭程序前确保所有操作已保存

系统使用
- 首次使用建议先添加房间和租户信息
- 租期选择支持：月租(30天)、季租(90天)、半年租(180天)、年租(365天)
- 起租日期可以选择自动（今天）或手动输入
- 财务统计会自动更新，无需手动计算

故障排除
- 如果程序无法启动，请检查是否有杀毒软件拦截
- 如果数据丢失，请检查数据文件是否存在
- 如果界面显示异常，请尝试重新启动程序
- 如果遇到其他问题，请重新安装程序

🔄 更新说明
v2.0 更新内容：
- 修复了租期选择窗口确认按钮显示问题
- 删除了"周租"选项，简化租期选择
- 优化了界面布局和用户体验
- 改进了数据文件处理机制
- 增强了系统稳定性

📞 技术支持
如遇到技术问题，请联系系统管理员。

版本信息
软件版本：v2.0
构建日期：2025-07-04
Python版本：3.13.2
PyInstaller版本：6.14.1

========================================
© 2025 房间管理系统 - 保留所有权利
