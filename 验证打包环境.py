#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证房间管理系统打包环境
检查所有必要的文件和依赖是否就绪
"""

import os
import sys
import subprocess
import json
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    print("🐍 检查Python版本...")
    version = sys.version_info
    print(f"   Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3:
        print("   ❌ 错误: 需要Python 3.x")
        return False
    elif version.major == 3 and version.minor < 7:
        print("   ⚠️  警告: 建议使用Python 3.7+")
        return True
    else:
        print("   ✅ Python版本符合要求")
        return True

def check_pyinstaller():
    """检查PyInstaller"""
    print("\n📦 检查PyInstaller...")
    try:
        import PyInstaller
        print(f"   ✅ PyInstaller已安装: {PyInstaller.__version__}")
        return True
    except ImportError:
        print("   ❌ PyInstaller未安装")
        print("   💡 安装命令: pip install pyinstaller")
        return False

def check_source_files():
    """检查源文件"""
    print("\n📄 检查源文件...")
    
    # 主程序文件
    main_files = ["房间管理系统2.py", "房间管理系统3.py", "房间管理系统.py"]
    main_file = None
    
    for file in main_files:
        if os.path.exists(file):
            main_file = file
            print(f"   ✅ 找到主程序: {file}")
            break
    
    if not main_file:
        print("   ❌ 错误: 未找到主程序文件")
        print("   💡 请确保以下文件之一存在:")
        for file in main_files:
            print(f"      - {file}")
        return False, None
    
    # 检查文件内容
    try:
        with open(main_file, 'r', encoding='utf-8') as f:
            content = f.read()
            if 'tkinter' in content and 'HotelManagementSystem' in content:
                print(f"   ✅ {main_file} 内容验证通过")
            else:
                print(f"   ⚠️  {main_file} 可能不是正确的主程序文件")
    except Exception as e:
        print(f"   ⚠️  无法读取 {main_file}: {e}")
    
    return True, main_file

def check_data_files():
    """检查数据文件"""
    print("\n📊 检查数据文件...")
    
    data_files = {
        "rooms.json": "房间数据",
        "persons.json": "人员数据", 
        "financial_data.json": "财务数据"
    }
    
    existing_files = []
    for file, desc in data_files.items():
        if os.path.exists(file):
            try:
                with open(file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                print(f"   ✅ {desc}文件存在且有效: {file}")
                existing_files.append(file)
            except json.JSONDecodeError:
                print(f"   ⚠️  {desc}文件存在但格式错误: {file}")
            except Exception as e:
                print(f"   ⚠️  {desc}文件读取失败: {file} - {e}")
        else:
            print(f"   ℹ️  {desc}文件不存在: {file} (运行时会创建)")
    
    return existing_files

def check_icon_file():
    """检查图标文件"""
    print("\n🎨 检查图标文件...")
    
    icon_file = "app_icon.ico"
    if os.path.exists(icon_file):
        size = os.path.getsize(icon_file)
        print(f"   ✅ 图标文件存在: {icon_file} ({size} bytes)")
        return icon_file
    else:
        print(f"   ℹ️  图标文件不存在: {icon_file}")
        print("   💡 可以运行 'python create_icon.py' 创建图标")
        return None

def check_build_scripts():
    """检查打包脚本"""
    print("\n🔧 检查打包脚本...")
    
    scripts = {
        "build_app.py": "自动化打包脚本",
        "build_simple.bat": "简化打包脚本",
        "create_icon.py": "图标创建脚本"
    }
    
    for script, desc in scripts.items():
        if os.path.exists(script):
            print(f"   ✅ {desc}存在: {script}")
        else:
            print(f"   ❌ {desc}不存在: {script}")

def test_import_dependencies():
    """测试导入依赖"""
    print("\n🔍 测试Python依赖...")
    
    dependencies = [
        ("tkinter", "GUI框架"),
        ("tkinter.ttk", "现代化控件"),
        ("tkinter.messagebox", "消息框"),
        ("tkinter.simpledialog", "简单对话框"),
        ("datetime", "日期时间"),
        ("json", "JSON处理"),
        ("os", "操作系统接口")
    ]
    
    failed_imports = []
    for module, desc in dependencies:
        try:
            __import__(module)
            print(f"   ✅ {desc}: {module}")
        except ImportError as e:
            print(f"   ❌ {desc}: {module} - {e}")
            failed_imports.append(module)
    
    return len(failed_imports) == 0

def generate_build_command(main_file, data_files, icon_file):
    """生成打包命令"""
    print("\n🚀 生成打包命令...")
    
    cmd_parts = [
        "pyinstaller",
        "--onefile",
        "--windowed", 
        "--name", "房间管理系统",
        "--clean"
    ]
    
    # 添加图标
    if icon_file:
        cmd_parts.extend(["--icon", icon_file])
    
    # 添加数据文件
    for data_file in data_files:
        cmd_parts.extend(["--add-data", f"{data_file};."])
    
    # 添加隐藏导入
    hidden_imports = ["tkinter", "tkinter.ttk", "tkinter.messagebox", "tkinter.simpledialog"]
    for module in hidden_imports:
        cmd_parts.extend(["--hidden-import", module])
    
    # 添加主文件
    cmd_parts.append(main_file)
    
    command = " ".join(cmd_parts)
    print(f"   推荐的打包命令:")
    print(f"   {command}")
    
    return command

def create_quick_build_script(command):
    """创建快速打包脚本"""
    script_content = f"""@echo off
chcp 65001 >nul
echo 🏨 房间管理系统快速打包
echo ========================

echo 正在执行打包命令...
{command}

if errorlevel 1 (
    echo ❌ 打包失败
    pause
    exit /b 1
)

echo ✅ 打包成功!
echo 📁 可执行文件位于: dist\\房间管理系统.exe

if not exist "release" mkdir "release"
copy "dist\\房间管理系统.exe" "release\\"

echo 📦 发布文件已复制到 release 目录
pause
"""
    
    with open("quick_build.bat", 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    print(f"\n📝 已创建快速打包脚本: quick_build.bat")

def main():
    """主函数"""
    print("🏨 房间管理系统打包环境验证")
    print("=" * 50)
    
    all_checks_passed = True
    
    # 检查Python版本
    if not check_python_version():
        all_checks_passed = False
    
    # 检查PyInstaller
    if not check_pyinstaller():
        all_checks_passed = False
    
    # 检查源文件
    source_ok, main_file = check_source_files()
    if not source_ok:
        all_checks_passed = False
    
    # 检查数据文件
    data_files = check_data_files()
    
    # 检查图标文件
    icon_file = check_icon_file()
    
    # 检查打包脚本
    check_build_scripts()
    
    # 测试依赖导入
    if not test_import_dependencies():
        all_checks_passed = False
    
    # 生成打包命令
    if source_ok:
        command = generate_build_command(main_file, data_files, icon_file)
        create_quick_build_script(command)
    
    # 总结
    print("\n" + "=" * 50)
    if all_checks_passed:
        print("🎉 环境验证通过！可以开始打包")
        print("\n📋 推荐的打包步骤:")
        print("1. 运行: python build_app.py")
        print("2. 或运行: quick_build.bat")
        print("3. 或手动执行上面显示的打包命令")
    else:
        print("❌ 环境验证失败，请解决上述问题后重试")
        print("\n💡 常见解决方案:")
        print("- 安装PyInstaller: pip install pyinstaller")
        print("- 确保主程序文件存在")
        print("- 检查Python环境是否正确")
    
    return all_checks_passed

if __name__ == "__main__":
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
