#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试房间管理系统的起租日期功能
验证自动计算和手动输入两种模式是否正常工作
"""

import tkinter as tk
from tkinter import messagebox
from datetime import datetime, timedelta
import sys
import os

def test_date_validation():
    """测试日期验证功能"""
    print("=== 测试日期验证功能 ===")
    
    def validate_date(date_str):
        """验证日期格式是否正确"""
        try:
            datetime.strptime(date_str, "%Y-%m-%d")
            return True
        except ValueError:
            return False
    
    # 测试用例
    test_cases = [
        ("2024-01-15", True, "正确的日期格式"),
        ("2024-1-15", False, "月份缺少前导零"),
        ("2024-01-5", False, "日期缺少前导零"),
        ("24-01-15", False, "年份格式错误"),
        ("2024/01/15", False, "分隔符错误"),
        ("2024-13-15", False, "月份超出范围"),
        ("2024-02-30", False, "日期不存在"),
        ("", False, "空字符串"),
        ("abc", False, "非日期字符串"),
    ]
    
    for date_str, expected, description in test_cases:
        result = validate_date(date_str)
        status = "✅ 通过" if result == expected else "❌ 失败"
        print(f"{status} - {description}: '{date_str}' -> {result}")
    
    print()

def test_date_calculation():
    """测试日期计算功能"""
    print("=== 测试日期计算功能 ===")
    
    # 测试起租日期
    start_date = datetime(2024, 1, 15).date()
    print(f"起租日期: {start_date}")
    
    # 测试不同租期的计算
    periods = [
        ("周租", timedelta(weeks=1)),
        ("月租", timedelta(days=30)),
        ("季租", timedelta(days=90)),
        ("半年租", timedelta(days=180)),
        ("年租", timedelta(days=365)),
    ]
    
    for period_name, period_delta in periods:
        end_date = start_date + period_delta
        print(f"{period_name}: {start_date} -> {end_date} (共{period_delta.days}天)")
    
    print()

def create_test_window():
    """创建测试窗口，模拟起租日期输入功能"""
    print("=== 创建测试窗口 ===")
    
    root = tk.Tk()
    root.title("起租日期功能测试")
    root.geometry("500x400")
    
    # 主框架
    main_frame = tk.Frame(root, padx=20, pady=20)
    main_frame.pack(fill="both", expand=True)
    
    # 标题
    title_label = tk.Label(main_frame, 
                          text="起租日期功能测试", 
                          font=("Microsoft YaHei UI", 16, "bold"))
    title_label.pack(pady=(0, 20))
    
    # 日期模式选择
    mode_frame = tk.Frame(main_frame)
    mode_frame.pack(fill="x", pady=(0, 15))
    
    tk.Label(mode_frame, text="起租日期设置:", font=("Microsoft YaHei UI", 12, "bold")).pack(side="left")
    
    date_mode_var = tk.StringVar(value="auto")
    
    auto_radio = tk.Radiobutton(mode_frame, 
                               text="自动计算（今天）", 
                               variable=date_mode_var, 
                               value="auto",
                               font=("Microsoft YaHei UI", 10))
    auto_radio.pack(side="right", padx=(10, 0))
    
    manual_radio = tk.Radiobutton(mode_frame, 
                                 text="手动输入", 
                                 variable=date_mode_var, 
                                 value="manual",
                                 font=("Microsoft YaHei UI", 10))
    manual_radio.pack(side="right", padx=(10, 0))
    
    # 起租日期输入
    date_frame = tk.Frame(main_frame)
    date_frame.pack(fill="x", pady=(0, 10))
    
    tk.Label(date_frame, text="起租日期:", font=("Microsoft YaHei UI", 12)).pack(side="left")
    
    start_date_entry = tk.Entry(date_frame, 
                               font=("Microsoft YaHei UI", 11),
                               width=15,
                               justify="center")
    start_date_entry.pack(side="right")
    
    # 日期格式提示
    hint_label = tk.Label(main_frame, 
                         text="(格式: YYYY-MM-DD，如: 2024-01-15)", 
                         font=("Microsoft YaHei UI", 9),
                         fg="gray")
    hint_label.pack(pady=(0, 15))
    
    # 租期选择
    period_frame = tk.Frame(main_frame)
    period_frame.pack(fill="x", pady=(0, 15))
    
    tk.Label(period_frame, text="租期类型:", font=("Microsoft YaHei UI", 12)).pack(side="left")
    
    period_var = tk.StringVar(value="月租")
    period_values = ["周租", "月租", "季租", "半年租", "年租"]
    
    from tkinter import ttk
    period_combobox = ttk.Combobox(period_frame, 
                                  textvariable=period_var, 
                                  values=period_values, 
                                  state="readonly",
                                  width=12)
    period_combobox.pack(side="right")
    
    # 截止日期显示
    end_date_frame = tk.Frame(main_frame)
    end_date_frame.pack(fill="x", pady=(0, 20))
    
    tk.Label(end_date_frame, text="截止日期:", font=("Microsoft YaHei UI", 12)).pack(side="left")
    
    end_date_entry = tk.Entry(end_date_frame, 
                             font=("Microsoft YaHei UI", 11),
                             width=15,
                             justify="center",
                             state="readonly")
    end_date_entry.pack(side="right")
    
    # 更新日期函数
    def update_dates(*args):
        mode = date_mode_var.get()
        period = period_var.get()
        
        if mode == "auto":
            # 自动计算模式
            today = datetime.now().date()
            start_date = today
            
            start_date_entry.config(state="normal")
            start_date_entry.delete(0, tk.END)
            start_date_entry.insert(0, start_date.strftime("%Y-%m-%d"))
            start_date_entry.config(state="readonly", bg="lightgray")
            
        else:
            # 手动输入模式
            start_date_entry.config(state="normal", bg="white")
            current_value = start_date_entry.get()
            if not current_value:
                start_date_entry.insert(0, datetime.now().date().strftime("%Y-%m-%d"))
            
            try:
                start_date = datetime.strptime(start_date_entry.get(), "%Y-%m-%d").date()
            except ValueError:
                start_date = datetime.now().date()
        
        # 计算截止日期
        if period == "周租":
            end_date = start_date + timedelta(weeks=1)
        elif period == "月租":
            end_date = start_date + timedelta(days=30)
        elif period == "季租":
            end_date = start_date + timedelta(days=90)
        elif period == "半年租":
            end_date = start_date + timedelta(days=180)
        elif period == "年租":
            end_date = start_date + timedelta(days=365)
        else:
            end_date = start_date + timedelta(days=30)
        
        # 更新截止日期显示
        end_date_entry.config(state="normal")
        end_date_entry.delete(0, tk.END)
        end_date_entry.insert(0, end_date.strftime("%Y-%m-%d"))
        end_date_entry.config(state="readonly")
    
    # 绑定事件
    period_var.trace_add("write", update_dates)
    date_mode_var.trace_add("write", update_dates)
    
    def on_start_date_change(event):
        if date_mode_var.get() == "manual":
            update_dates()
    
    start_date_entry.bind('<KeyRelease>', on_start_date_change)
    start_date_entry.bind('<FocusOut>', on_start_date_change)
    
    # 测试按钮
    def test_validation():
        start_date_str = start_date_entry.get().strip()
        if not start_date_str:
            messagebox.showerror("错误", "请输入起租日期")
            return
        
        try:
            start_date = datetime.strptime(start_date_str, "%Y-%m-%d").date()
            messagebox.showinfo("验证成功", f"起租日期格式正确: {start_date}")
        except ValueError:
            messagebox.showerror("验证失败", "起租日期格式不正确，请使用 YYYY-MM-DD 格式")
    
    test_btn = tk.Button(main_frame, 
                        text="测试日期验证", 
                        command=test_validation,
                        font=("Microsoft YaHei UI", 10),
                        bg="lightblue")
    test_btn.pack(pady=10)
    
    # 初始化
    update_dates()
    
    print("测试窗口已创建，请手动测试以下功能：")
    print("1. 切换自动计算和手动输入模式")
    print("2. 在手动输入模式下输入不同格式的日期")
    print("3. 选择不同的租期类型，观察截止日期变化")
    print("4. 点击'测试日期验证'按钮验证日期格式")
    
    root.mainloop()

if __name__ == "__main__":
    print("房间管理系统 - 起租日期功能测试")
    print("=" * 50)
    
    # 运行自动化测试
    test_date_validation()
    test_date_calculation()
    
    # 创建交互式测试窗口
    create_test_window()
